import React, { forwardRef } from "react";
import {
  Stack,
  InputLabel,
  OutlinedInput,
  Select,
  MenuItem,
} from "@mui/material";
import { pxToRem } from "@u/zkUtils";
import RequirePoint from "./RequirePoint";
import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
import { useTheme } from "@mui/material/styles";
import { t } from "i18next";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

function getStyles(name, personName, theme) {
  return {
    fontWeight: personName.includes(name)
      ? theme.typography.fontWeightMedium
      : theme.typography.fontWeightRegular,
  };
}

const CustomSelect = forwardRef((props, ref) => {
  const theme = useTheme();
  const {
    handlechange,
    name,
    label,
    required,
    items = [],
    error,
    formik,
    labelpostion = "left",
    disabled = false,
    placeholder = t("common.common_enter"),
    labelOptions = { label: "label", value: "value" },
    ...other
  } = props;

  const handlerChangeSelect = (event) => {
    // if (formik?.handlechange) {
    //   formik?.onChange(event);
    // }
    // if (handlechange) {
    //   onChange(event);
    // }

    if (formik?.handleChange) {
      formik.handleChange(event);
    }
    // 调用外部传入的 handlechange 方法
    if (handlechange) {
      handlechange(event);
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "column" : "row"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <Select
            sx={{
              width: pxToRem(264),
              height: pxToRem(50),
              borderRadius: "5px",
              ".MuiSelect-select": {
                padding: "5px",
              },
            }}
            displayEmpty
            {...other}
            value={formik?.values[name]}
            onChange={handlerChangeSelect}
            input={<OutlinedInput />}
            name={name}
            disabled={disabled}
            MenuProps={MenuProps}
            inputProps={{ "aria-label": "Without label" }}
            IconComponent={ExpandMoreOutlinedIcon}>
            <MenuItem disabled value="">
              <span
                style={{
                  color: "#a4a4a4",
                  fontSize: "14px",
                }}>
                {placeholder}
              </span>
            </MenuItem>
            {items?.map((item) => {
              return (
                <MenuItem
                  key={item[labelOptions.value]}
                  value={item[labelOptions.value]?.toString()}>
                  {item[labelOptions.label]}
                </MenuItem>
              );
            })}
          </Select>
        </Stack>

        {((formik?.touched[name] && formik?.errors[name]) || error) && (
          <FormHelperText error id={`standard-weight-helper-text-${name}`}>
            {formik?.errors[name] || error}
          </FormHelperText>
        )}
      </Stack>
    </Stack>
  );
});

export default CustomSelect;
