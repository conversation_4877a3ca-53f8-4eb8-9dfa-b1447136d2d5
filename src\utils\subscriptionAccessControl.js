/**
 * 基于订阅状态的微前端访问控制工具
 * 防止用户在未订阅的情况下通过URL直接访问子应用
 */

// 微前端应用配置
const MICRO_APP_CONFIG = {
  '/cms-app': {
    name: 'Screen Direct',
    code: 'SD',
    appCenterPath: '/application-center'
  },
  '/retail-ai-app': {
    name: '<PERSON><PERSON>',
    code: 'ZT',
    appCenterPath: '/application-center'
  },
  '/e-price-tag-app': {
    name: 'NuTag',
    code: 'NT',
    appCenterPath: '/application-center'
  }
};

/**
 * 检查路径是否为微前端路由
 * @param {string} pathname - 路径
 * @returns {boolean} 是否为微前端路由
 */
export const isMicroAppRoute = (pathname) => {
  return Object.keys(MICRO_APP_CONFIG).some(route => pathname.startsWith(route));
};

/**
 * 获取路径对应的应用代码
 * @param {string} pathname - 路径
 * @returns {string|null} 应用代码
 */
export const getAppCodeFromPath = (pathname) => {
  for (const [route, config] of Object.entries(MICRO_APP_CONFIG)) {
    if (pathname.startsWith(route)) {
      return config.code;
    }
  }
  return null;
};

/**
 * 获取路径对应的应用名称
 * @param {string} pathname - 路径
 * @returns {string|null} 应用名称
 */
export const getAppNameFromPath = (pathname) => {
  for (const [route, config] of Object.entries(MICRO_APP_CONFIG)) {
    if (pathname.startsWith(route)) {
      return config.name;
    }
  }
  return null;
};

/**
 * 获取应用中心路径
 * @param {string} pathname - 当前路径
 * @returns {string} 应用中心路径
 */
export const getAppCenterPath = (pathname) => {
  for (const [route, config] of Object.entries(MICRO_APP_CONFIG)) {
    if (pathname.startsWith(route)) {
      return config.appCenterPath;
    }
  }
  return '/application-center';
};

/**
 * 检查用户是否订阅了指定应用
 * @param {Array} appList - 应用列表
 * @param {string} appCode - 应用代码
 * @returns {boolean} 是否已订阅
 */
export const isAppSubscribed = (appList, appCode) => {
  if (!appList || !Array.isArray(appList)) {
    // console.warn('应用列表为空或格式不正确');
    return false;
  }

  const app = appList.find((app) => app.code === appCode);

  if (!app) {
    // console.warn(`未找到应用代码为 ${appCode} 的应用`);
    return false;
  }

  const isSubscribed = app.isSubscribed === "Y";

  // console.log(`📋 订阅状态检查: ${app.name || appCode} - ${isSubscribed ? '已订阅' : '未订阅'} (${app.isSubscribed})`);

  return isSubscribed;
};

/**
 * 验证微前端应用访问权限
 * @param {string} pathname - 访问路径
 * @param {Array} appList - 用户应用列表
 * @returns {Object} 验证结果 { allowed: boolean, reason: string, appName: string, appCode: string }
 */
export const validateMicroAppAccess = (pathname, appList) => {
  // 检查是否为微前端路由
  if (!isMicroAppRoute(pathname)) {
    return { allowed: true, reason: 'not_micro_app' };
  }

  const appCode = getAppCodeFromPath(pathname);
  const appName = getAppNameFromPath(pathname);

  if (!appCode) {
    return {
      allowed: false,
      reason: 'invalid_app_code',
      appName,
      appCode
    };
  }

  // 检查订阅状态
  const subscribed = isAppSubscribed(appList, appCode);

  if (!subscribed) {
    // console.warn(`❌ 访问被拒绝: 用户未订阅 ${appName} (${appCode})`);
    return {
      allowed: false,
      reason: 'not_subscribed',
      appName,
      appCode
    };
  }

  // console.log(`✅ 访问权限验证通过: ${appName} (${appCode})`);
  return {
    allowed: true,
    reason: 'subscribed',
    appName,
    appCode
  };
};

/**
 * 获取订阅状态描述
 * @param {Array} appList - 应用列表
 * @param {string} appCode - 应用代码
 * @returns {Object} 订阅状态信息
 */
export const getSubscriptionStatus = (appList, appCode) => {
  if (!appList || !Array.isArray(appList)) {
    return {
      subscribed: false,
      status: 'unknown',
      message: '无法获取订阅状态'
    };
  }

  const app = appList.find((app) => app.code === appCode);

  if (!app) {
    return {
      subscribed: false,
      status: 'not_found',
      message: '应用不存在'
    };
  }

  const subscribed = app.isSubscribed === "Y";

  return {
    subscribed,
    status: app.isSubscribed,
    message: subscribed ? '已订阅' : '未订阅',
    appName: app.name || appCode,
    appCode: app.code
  };
};

/**
 * 获取所有应用的订阅状态
 * @param {Array} appList - 应用列表
 * @returns {Object} 所有应用的订阅状态
 */
export const getAllSubscriptionStatus = (appList) => {
  const status = {};

  Object.values(MICRO_APP_CONFIG).forEach(config => {
    status[config.code] = getSubscriptionStatus(appList, config.code);
  });

  return status;
};

/**
 * 检查用户是否有任何已订阅的应用
 * @param {Array} appList - 应用列表
 * @returns {boolean} 是否有已订阅的应用
 */
export const hasAnySubscription = (appList) => {
  if (!appList || !Array.isArray(appList)) {
    return false;
  }

  return Object.values(MICRO_APP_CONFIG).some(config =>
    isAppSubscribed(appList, config.code)
  );
};

/**
 * 获取已订阅的应用列表
 * @param {Array} appList - 应用列表
 * @returns {Array} 已订阅的应用列表
 */
export const getSubscribedApps = (appList) => {
  if (!appList || !Array.isArray(appList)) {
    return [];
  }

  return Object.values(MICRO_APP_CONFIG).filter(config =>
    isAppSubscribed(appList, config.code)
  );
};

/**
 * 获取未订阅的应用列表
 * @param {Array} appList - 应用列表
 * @returns {Array} 未订阅的应用列表
 */
export const getUnsubscribedApps = (appList) => {
  if (!appList || !Array.isArray(appList)) {
    return Object.values(MICRO_APP_CONFIG);
  }

  return Object.values(MICRO_APP_CONFIG).filter(config =>
    !isAppSubscribed(appList, config.code)
  );
};

export default {
  isMicroAppRoute,
  getAppCodeFromPath,
  getAppNameFromPath,
  getAppCenterPath,
  isAppSubscribed,
  validateMicroAppAccess,
  getSubscriptionStatus,
  getAllSubscriptionStatus,
  hasAnySubscription,
  getSubscribedApps,
  getUnsubscribedApps
};
