import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { getProductDetail, getProductLabel } from "@s/api/product";
import { useClientId, useClientCode } from "@/hooks/client.js";
import { useLocation, useNavigate } from "react-router-dom";
import ViewBox from "@/components/ViewBox";
function ViewOutlet(props) {
  const navigate = useNavigate();
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();
  const [data, setData] = useState([]);
  const [imageUrl, setImageUrl] = useState("");
  const [customTag, setCustomTag] = useState([]);
  const [newLabels, setNewLabels] = useState([]);
  const clientId = useClientId();
  const clinetCode = useClientCode();
  if (clientId) {
    sessionStorage.setItem("CLIENT_ID", clientId);
    sessionStorage.setItem("CLIENT_CODE", clinetCode);
  }

  useEffect(() => {
    // 商品自定义输入框方法
    getProductLabel({ departmentId: sessionStorage.getItem("CLIENT_ID") }).then(
      (res) => {
        setNewLabels(res?.data);
      }
    );
    getProductDetail(state?.id).then((res) => {
      setImageUrl(res?.data?.productPhotoList[0]?.photoUrl);
      let data = res?.data;

      setCustomTag(res?.data?.productTypeValueList);
      setData(data);
    });
  }, []);

  return (
    <div
      style={{
        height: "100%",
      }}>
      <RightViewLayout
        title={"View Product"}
        navigateBack={"/retail/list"}
        handleCancle={() => {
          navigate("/retail/list");
        }}
        isShowSave={false}>
        <Grid>
          <Avatar
            className="avatar radial-button"
            alt="加载失败"
            sx={{ width: "110px", height: "110px", borderRadius: "8px" }}
            src={imageUrl}></Avatar>
        </Grid>

        <Grid item mt={3}>
          <ViewBox title={t("product.name")} content={data?.name} />
        </Grid>

        {newLabels?.map((item, index) => {
          let response = customTag?.find((list) => {
            return list?.productTypeId == item?.id;
          });

          return (
            <Grid item mt={3} key={item?.id + index}>
              <ViewBox title={item?.name} content={response?.value} />
            </Grid>
          );
        })}
      </RightViewLayout>
    </div>
  );
}

export default ViewOutlet;
