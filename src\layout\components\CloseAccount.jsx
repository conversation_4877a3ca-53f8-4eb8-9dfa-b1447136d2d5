import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Button,
  Box,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { Visibility, VisibilityOff, Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { closeAccount } from "@/service/api/user.js";
import { useDispatch } from "react-redux";
import { removeToken } from "@/utils/auth";
import { clearUser } from "@/store/reducers/user";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
const CloseAccount = ({ open, setOpen }) => {
  const { t } = useTranslation();
  const [password, setPassword] = useState("");
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
    if (error) setError(""); // 清除错误信息
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleCancel = () => {
    setPassword("");
    setError("");
    setOpen(false);
  };

  const handleConfirm = async () => {
    if (!password.trim()) {
      setError(t("account_delete.error_password_required"));
      return;
    }

    setLoading(true);
    try {
      // 调用删除账户的回调函数
      const res = await closeAccount({ password: password });
      if (res?.code == "********") {
        toast.success(t("account_delete.success_message"));

        setTimeout(() => {
          navigate("/login");
          removeToken();
          dispatch(clearUser());
          setOpen(false);
        }, 2000);
      }
      setPassword("");
      setError("");
    } catch (err) {
      setError(err.message || t("account_delete.error_delete_failed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          padding: 1,
          width: "580px",
        },
      }}>
      {/* 标题栏 */}
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
          fontSize: "18px",
          fontWeight: 600,
          color: "#333",
        }}>
        {t("account_delete.title")}
        <IconButton
          onClick={handleCancel}
          size="small"
          sx={{
            color: "#999",
            "&:hover": {
              color: "#666",
            },
          }}>
          <Close />
        </IconButton>
      </DialogTitle>

      {/* 内容区域 */}
      <DialogContent sx={{ pt: 1, pb: 3, mt: 3 }}>
        <Box>
          {/* 确认问题 */}
          <Typography
            variant="body1"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: 500,
              fontSize: "14px",
            }}>
            {t("account_delete.confirmation_question")}
          </Typography>

          {/* 警告信息 */}
          <Typography
            variant="body2"
            sx={{
              mb: 3,
              color: "#666",
              lineHeight: 1.5,
              fontSize: "14px",
            }}>
            {t("account_delete.warning_message")}
          </Typography>

          {/* 密码输入提示 */}
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              color: "#333",
              fontWeight: 500,
              fontSize: "14px",
            }}>
            {t("account_delete.password_prompt")}
          </Typography>

          {/* 密码输入框 */}
          <TextField
            fullWidth
            label={t("account_delete.current_password_label")}
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={handlePasswordChange}
            error={!!error}
            helperText={error}
            disabled={loading}
            placeholder={t("account_delete.password_placeholder")}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 1,
              },
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleTogglePasswordVisibility}
                    edge="end"
                    disabled={loading}
                    sx={{ color: "#999" }}>
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </DialogContent>

      {/* 按钮区域 */}
      <DialogActions
        sx={{
          px: 3,
          pb: 3,
          pt: 0,
          gap: 2,
        }}>
        <Button
          onClick={() => setOpen(false)}
          variant="outlined"
          disabled={loading}
          sx={{
            minWidth: 100,
            borderRadius: 1,
            textTransform: "none",
            borderColor: "#ddd",
            color: "#666",
            "&:hover": {
              borderColor: "#bbb",
              backgroundColor: "#f5f5f5",
            },
          }}>
          {t("account_delete.cancel_button")}
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={loading || !password.trim()}
          sx={{
            minWidth: 120,
            borderRadius: 1,
            textTransform: "none",
            backgroundColor: "#f44336",
            "&:hover": {
              backgroundColor: "#d32f2f",
            },
            "&:disabled": {
              backgroundColor: "#ffcdd2",
              color: "#fff",
            },
          }}>
          {loading
            ? t("account_delete.deleting_button")
            : t("account_delete.delete_button")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CloseAccount;
