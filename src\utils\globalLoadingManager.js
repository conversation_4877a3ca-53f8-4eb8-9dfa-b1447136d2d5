/**
 * 全局加载状态管理器
 * 用于管理应用级别的加载状态，特别是微前端切换时的加载体验
 */

class GlobalLoadingManager {
  constructor() {
    this.listeners = new Set();
    this.state = {
      isLoading: false,
      message: '正在加载...',
      progress: 0,
      showProgress: false,
      type: 'default' // default, transition, refresh
    };
  }

  /**
   * 添加状态监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Loading state listener error:', error);
      }
    });
  }

  /**
   * 更新状态
   * @param {Object} newState - 新状态
   */
  updateState(newState) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  /**
   * 开始加载
   * @param {Object} options - 加载选项
   */
  startLoading(options = {}) {
    const {
      message = '正在加载...',
      showProgress = false,
      type = 'default'
    } = options;

    this.updateState({
      isLoading: true,
      message,
      progress: 0,
      showProgress,
      type
    });
  }

  /**
   * 更新进度
   * @param {number} progress - 进度值 (0-100)
   * @param {string} message - 可选的消息更新
   */
  updateProgress(progress, message) {
    const updates = { progress: Math.min(100, Math.max(0, progress)) };
    if (message) {
      updates.message = message;
    }
    this.updateState(updates);
  }

  /**
   * 更新消息
   * @param {string} message - 新消息
   */
  updateMessage(message) {
    this.updateState({ message });
  }

  /**
   * 完成加载
   */
  finishLoading() {
    if (this.state.showProgress) {
      this.updateState({ progress: 100 });
      setTimeout(() => {
        this.updateState({
          isLoading: false,
          progress: 0,
          showProgress: false,
          type: 'default'
        });
      }, 500);
    } else {
      this.updateState({
        isLoading: false,
        progress: 0,
        showProgress: false,
        type: 'default'
      });
    }
  }

  /**
   * 取消加载
   */
  cancelLoading() {
    this.updateState({
      isLoading: false,
      progress: 0,
      showProgress: false,
      type: 'default'
    });
  }

  /**
   * 开始应用切换加载
   */
  startAppTransition(fromApp, toApp) {
    const message = fromApp 
      ? `正在从 ${fromApp} 切换到 ${toApp}...`
      : `正在加载 ${toApp}...`;
    
    this.startLoading({
      message,
      showProgress: true,
      type: 'transition'
    });

    // 模拟切换进度
    this.simulateTransitionProgress();
  }

  /**
   * 开始页面刷新加载
   */
  startPageRefresh() {
    this.startLoading({
      message: '正在刷新页面...',
      showProgress: true,
      type: 'refresh'
    });

    // 模拟刷新进度
    this.simulateRefreshProgress();
  }

  /**
   * 模拟切换进度
   */
  simulateTransitionProgress() {
    const steps = [
      { progress: 20, message: '正在清理当前应用状态...' },
      { progress: 40, message: '正在加载目标应用...' },
      { progress: 60, message: '正在初始化应用...' },
      { progress: 80, message: '正在渲染界面...' },
      { progress: 100, message: '切换完成' }
    ];

    this.executeProgressSteps(steps, 300);
  }

  /**
   * 模拟刷新进度
   */
  simulateRefreshProgress() {
    const steps = [
      { progress: 30, message: '正在保存状态...' },
      { progress: 60, message: '正在重新加载资源...' },
      { progress: 90, message: '正在恢复状态...' },
      { progress: 100, message: '刷新完成' }
    ];

    this.executeProgressSteps(steps, 400);
  }

  /**
   * 执行进度步骤
   * @param {Array} steps - 进度步骤
   * @param {number} stepDuration - 每步持续时间
   */
  executeProgressSteps(steps, stepDuration = 300) {
    let currentStep = 0;

    const executeNext = () => {
      if (currentStep < steps.length && this.state.isLoading) {
        const step = steps[currentStep];
        this.updateProgress(step.progress, step.message);
        currentStep++;
        
        if (currentStep < steps.length) {
          setTimeout(executeNext, stepDuration);
        } else {
          setTimeout(() => this.finishLoading(), stepDuration);
        }
      }
    };

    executeNext();
  }

  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.state };
  }
}

// 创建全局实例
const globalLoadingManager = new GlobalLoadingManager();

export default globalLoadingManager;
