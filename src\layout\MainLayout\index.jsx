// src/layout/MainLayout/index.jsx
import { Outlet, useLocation } from "react-router-dom";
import { useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@mui/material/styles";
import { Backdrop, CircularProgress, Box } from "@mui/material";
import { useMediaQuery } from "@mui/material";
import Drawer from "./Drawer"; // 拆分后的抽屉组件
import DrawerToggleButton from "../components/DrawerToggleButton"; // 拆分后的按钮组件
import { openDrawer } from "@/store/reducers/menu";

import bg from "@/assets/Images/BroundImage/GlobalBg.png";

const MainLayout = () => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const location = useLocation();
  const matchDownLG = useMediaQuery(theme.breakpoints.down("xl"));

  // 使用 Redux 状态
  const { drawerOpen, menuList } = useSelector((state) => state.menu);

  // 检测是否为异常页面（不显示菜单栏）
  const isExceptionPage =
    ["/404", "/403", "/500"].includes(location.pathname) ||
    location.pathname.startsWith("/error");

  // 抽屉切换处理函数
  const handleDrawerToggle = useCallback(() => {
    dispatch(openDrawer({ drawerOpen: !drawerOpen }));
  }, [dispatch, drawerOpen]);

  // 响应式处理
  useEffect(() => {
    dispatch(openDrawer({ drawerOpen: !matchDownLG }));
  }, [matchDownLG, dispatch]);

  // 如果是异常页面，直接渲染内容，不显示菜单栏
  if (isExceptionPage) {
    return (
      <Box
        sx={{
          width: "100vw",
          height: "100vh",
          overflow: "auto",
        }}>
        <Outlet />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        backgroundImage: `url(${bg})`,
        overflow: "auto",
        width: "100vw",
        height: "100vh",
        padding: "20px",
        boxSizing: "border-box", // 修正拼写错误：borderSizing -> boxSizing
        overflowX: "hidden",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
        position: "relative",
      }}>
      {/* 全局加载遮罩 */}
      <Backdrop
        sx={{ color: "#fff", zIndex: 99999 }}
        open={false} // 根据实际 loading 状态控制
      >
        <CircularProgress color="inherit" />
      </Backdrop>
      {/* 抽屉组件 */}
      <Drawer
        open={drawerOpen}
        toggleDrawer={handleDrawerToggle}
        width={255}
        menuList={menuList}
      />
      {/* 悬浮按钮组件 */}
      <DrawerToggleButton open={drawerOpen} onClick={handleDrawerToggle} />

      {/* 主内容区域 */}
      <Box
        sx={{
          width: "100%",
          height: "100%",
          overflowX: "hidden",
          paddingLeft: drawerOpen ? "280px" : "84px",
          transition: "padding-left 0.6s ease-in-out",
        }}>
        {/* 渲染路由内容，包括微前端应用 */}
        <Outlet />
      </Box>
    </Box>
  );
};

export default MainLayout;
