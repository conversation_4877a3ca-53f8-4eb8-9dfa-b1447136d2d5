// FILEPATH: d:/A EPL-dev/main-app/src/service/api/subscription.js

import request from "@u/request";

/**
 * 新增订阅记录
 * <AUTHOR>
 * @date 2025-03-11 11:30
 * @param {Object} params - 订阅记录参数
 * @returns {Promise} 请求的Promise对象
 */
export const addSubscription = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * 修改订阅记录
 * <AUTHOR>
 * @date 2025-03-11 11:31
 * @param {Object} params - 修改的订阅记录参数
 * @returns {Promise} 请求的Promise对象
 */
export const editSubscription = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/operate`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * 获取货币单位列表
 * <AUTHOR>
 * @date 2025-03-11 11:32
 * @returns {Promise} 请求的Promise对象
 */
export const getUnitList = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/currency/unit`,
    method: "GET",
  });
};

/**
 * 通过应用ID查询其所有套餐包(可对接)
 * <AUTHOR>
 * @date 2025-03-11 11:33
 * @param {Object} params - 查询参数，包含应用ID
 * @returns {Promise} 请求的Promise对象
 */
export const getAllPackage = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/package/list`,
    method: "GET",
    params: params,
  });
};

/**
 * 通过id查询套餐详情
 * <AUTHOR>
 * @date 2025-03-11 11:34
 * @param {string|number} id - 套餐ID
 * @returns {Promise} 请求的Promise对象
 */
export const getPackageDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/package/query/${id}`,
    method: "GET",
  });
};

/**
 * 通过id查询套餐详情
 * <AUTHOR>
 * @date 2025-03-11 11:34
 * @param {string|number} id - 套餐ID
 * @returns {Promise} 请求的Promise对象
 */
export const getSubscriptionList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/query/page`,
    method: "GET",
    params: params,
  });
};



/**
 *   左下角我的订阅
 * 
 *   查看当前用户订阅信息
 * 
 */
export const getMySubscription = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/subscription/user`,
    method: "GET",
  });
};

