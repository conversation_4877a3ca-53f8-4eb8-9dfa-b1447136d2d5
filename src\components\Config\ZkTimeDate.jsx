import React, { useState } from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import DateIcon from "@/assets/Icons/Date.svg?react";
import RequirePoint from "../RequirePoint";
import { useEffect } from "react";
import i18n from "i18next";
function ZkTimeDate(props) {
  const {
    formik,
    name,
    label,
    error,
    handleChange,
    placeholder = i18n.t("common.common_please_select_com"),
    labelpostion,
    disabled = false,
    views = ["year", "month", "day"],
    dateFormat = "YYYY-MM-DD",
    minDate,
    maxDate,
  } = props;
  // 使用 dayjs 来初始化日期值，如果没有初始值则为 null
  const [value, setValue] = useState(null);

  useEffect(() => {
    let values = formik?.values[name] ? dayjs(formik.values[name]) : null;

    if (values) {
      setValue(values);
    } else {
      setValue(null);
    }
  }, [formik.values[name]]);

  const CustomIcon = (props) => (
    <DateIcon
      {...props}
      sx={{
        color: "blue",
        fontSize: "1.5rem",
      }}
    />
  );

  const changeFn = (newValue) => {
    // 确保 newValue 是一个有效的 dayjs 对象
    const validValue = newValue ? newValue : null;
    setValue(validValue);

    if (formik?.handleChange) {
      // 将 dayjs 对象转换为字符串或者你需要的格式
      formik.setFieldValue(
        name,
        validValue ? validValue.format(dateFormat) : null
      );
    }

    if (handleChange) {
      handleChange(validValue);
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion == "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              format={dateFormat}
              onChange={changeFn}
              name={name}
              disabled={disabled}
              placement="bottom-right"
              views={views}
              slotProps={{
                textField: {
                  placeholder: placeholder,
                },
              }}
              components={{
                OpenPickerIcon: CustomIcon,
                rightArrowIcon: CustomIcon, // 使用自定义图标
              }}
              sx={{
                "& .MuiInputBase-input": {
                  fontSize: "14px",
                },
              }}
              maxDate={maxDate}
              minDate={minDate}
              openPickerButton={<CustomIcon />}
              componentsProps={{
                openPickerButton: {
                  color: "primary", // 可以自定义图标的颜色
                },
              }}
              value={value}
              renderInput={(params) => (
                <TextField
                  {...params}
                  inputProps={{
                    ...params.InputProps,
                  }}
                  error={Boolean(
                    (formik?.touched[name] && formik?.errors[name]) || error
                  )}
                />
              )}
            />

            {((formik?.touched[name] && formik?.errors[name]) || error) && (
              <FormHelperText error id={`standard-weight-helper-text-${name}`}>
                {formik?.errors[name] || error}
              </FormHelperText>
            )}
          </LocalizationProvider>
        </Stack>
      </Stack>
    </Stack>
  );
}

export default ZkTimeDate;
