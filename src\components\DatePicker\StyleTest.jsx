import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  Stack,
  Alert,
  AlertTitle
} from '@mui/material';
import DatePicker from './index';
import dayjs from 'dayjs';

const StyleTest = () => {
  const [basicValue, setBasicValue] = useState();
  const [timeValue, setTimeValue] = useState();
  const [dateTimeValue, setDateTimeValue] = useState();
  const [rangeValue, setRangeValue] = useState([]);
  const [monthValue, setMonthValue] = useState();
  const [yearValue, setYearValue] = useState();

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        DatePicker 样式测试
      </Typography>

      <Alert severity="info" sx={{ mb: 4 }}>
        <AlertTitle>问题修复说明</AlertTitle>
        <Typography variant="body2">
          • ✅ 修复了基础日期选择无法选择的问题<br/>
          • ✅ 修复了日期范围选择无法选择的问题<br/>
          • ✅ 修复了年份选择显示不全的问题<br/>
          • ✅ 修复了时间选择器立即回填的问题，现在会延迟回填<br/>
          • ✅ 优化了所有组件的样式和交互体验
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 基础日期选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅 基础日期选择
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择日期</FormLabel>
              <DatePicker
                value={basicValue}
                onChange={setBasicValue}
                placeholder="请选择日期"
                allowClear
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中值: {basicValue ? basicValue.format('YYYY-MM-DD') : '未选择'}
            </Typography>
          </Paper>
        </Grid>

        {/* 日期时间选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅⏰ 日期时间选择 (showTime)
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择日期时间</FormLabel>
              <DatePicker
                value={dateTimeValue}
                onChange={setDateTimeValue}
                placeholder="请选择日期时间"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                allowClear
                needConfirm
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中值: {dateTimeValue ? dateTimeValue.format('YYYY-MM-DD HH:mm:ss') : '未选择'}
            </Typography>
          </Paper>
        </Grid>

        {/* 纯时间选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              ⏰ 纯时间选择
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择时间</FormLabel>
              <DatePicker.TimePicker
                value={timeValue}
                onChange={setTimeValue}
                placeholder="请选择时间"
                format="HH:mm:ss"
                allowClear
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中值: {timeValue ? timeValue.format('HH:mm:ss') : '未选择'}
            </Typography>
          </Paper>
        </Grid>

        {/* 日期范围选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅📅 日期范围选择
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择日期范围</FormLabel>
              <DatePicker.RangePicker
                value={rangeValue}
                onChange={setRangeValue}
                placeholder={["开始日期", "结束日期"]}
                allowClear
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中范围: {rangeValue && rangeValue[0] && rangeValue[1]
                ? `${rangeValue[0].format('YYYY-MM-DD')} ~ ${rangeValue[1].format('YYYY-MM-DD')}`
                : '未选择'}
            </Typography>
          </Paper>
        </Grid>

        {/* 月份选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅 月份选择
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择月份</FormLabel>
              <DatePicker
                value={monthValue}
                onChange={setMonthValue}
                placeholder="请选择月份"
                picker="month"
                format="YYYY-MM"
                allowClear
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中月份: {monthValue ? monthValue.format('YYYY-MM') : '未选择'}
            </Typography>
          </Paper>
        </Grid>

        {/* 年份选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅 年份选择
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择年份</FormLabel>
              <DatePicker
                value={yearValue}
                onChange={setYearValue}
                placeholder="请选择年份"
                picker="year"
                format="YYYY"
                allowClear
              />
            </FormControl>
            <Typography variant="body2" color="text.secondary">
              选中年份: {yearValue ? yearValue.format('YYYY') : '未选择'}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Alert severity="success">
          <AlertTitle>✅ 样式修复完成</AlertTitle>
          <Stack spacing={1}>
            <Typography variant="body2">
              <strong>时间选择器:</strong> 使用三列布局（时/分/秒），类似 Ant Design 风格
            </Typography>
            <Typography variant="body2">
              <strong>日期时间选择:</strong> 左右布局，日期在左，时间在右
            </Typography>
            <Typography variant="body2">
              <strong>日期范围选择:</strong> 双日历并排显示，范围高亮效果
            </Typography>
            <Typography variant="body2">
              <strong>弹出样式:</strong> 参考 Ant Design 阴影和圆角，保持 Material-UI 输入框
            </Typography>
          </Stack>
        </Alert>
      </Box>
    </Box>
  );
};

export default StyleTest;
