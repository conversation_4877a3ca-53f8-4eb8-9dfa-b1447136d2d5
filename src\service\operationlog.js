import request from "@/utils/request";

/**
 *  查询操作日志列表
 */
export const operationlogList = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/oper_log/query/page`,
    method: "get",
    params: params,
  });
};


/**
 *  查询操作日志详情
 * 
 */

export const operationlogDetail = (id) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/oper_log/query/${id}`,
    method: "get",
  });
};