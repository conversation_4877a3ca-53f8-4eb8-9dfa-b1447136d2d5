import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper,
  Popover,
  TextField,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  LocalizationProvider,
  DateCalendar,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  CalendarToday as CalendarIcon,
} from "@mui/icons-material";
import dayjs from 'dayjs';

const MinimalTest = () => {
  const [value, setValue] = useState();
  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleInputClick = (e) => {
    setAnchorEl(e.currentTarget);
    setOpen(true);
  };

  const handleDateChange = (newValue) => {
    console.log('Date changed:', newValue);
    setValue(newValue);
    setOpen(false);
    setAnchorEl(null);
  };

  const handleClose = () => {
    setOpen(false);
    setAnchorEl(null);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        最简单的日期选择测试
      </Typography>
      
      <Paper sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          📅 最基础的日期选择
        </Typography>
        
        <TextField
          value={value ? value.format('YYYY-MM-DD') : ''}
          placeholder="请选择日期"
          onClick={handleInputClick}
          InputProps={{
            readOnly: true,
            style: { cursor: 'pointer' },
            endAdornment: (
              <InputAdornment position="end">
                <CalendarIcon />
              </InputAdornment>
            ),
          }}
          fullWidth
          sx={{ mb: 2 }}
        />
        
        <Popover
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
        >
          <Box sx={{ p: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
              <DateCalendar
                value={value}
                onChange={handleDateChange}
                sx={{
                  '& .MuiPickersDay-root': {
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'primary.light',
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </Box>
        </Popover>
        
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>当前值:</strong> {value ? value.format('YYYY-MM-DD') : '未选择'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>测试说明:</strong> 这是最简单的实现，如果这个能工作，说明基础功能正常
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default MinimalTest;
