import { Grid } from "@mui/material";
import { pxToRem } from "@/utils/zkUtils";
import Screen_Direct from "@/assets/Home/<EMAIL>";
import NuTag from "@/assets/Home/<EMAIL>";
import Zata from "@/assets/Home/<EMAIL>";
import user from "@/assets/Images/user.png";
import NuTag1 from "@/assets/Home/<EMAIL>";
import Zata1 from "@/assets/Home/<EMAIL>";
import Screen_Direct1 from "@/assets/Home/<EMAIL>";
import PhoneblueESL from "@/assets/Home/<EMAIL>";
import pngegg1 from "@/assets/Home/<EMAIL>";
import pngegg from "@/assets/Home/<EMAIL>";
import MaskGroup from "@/assets/Home/<EMAIL>";
import notif from "@/assets/Home/notif.png";
import notif2 from "@/assets/Home/<EMAIL>";
import Copy from "@/assets/Home/<EMAIL>";

import facebook from "@/assets/Home/facebook.png";
import instagram from "@/assets/Home/instagram.png";
import linkedin from "@/assets/Home/linkedin.png";
import videoIcon from "@/assets/Home/video.png";
import CheckIcon from "@/assets/menuIcon/CheckStatus.svg";
import { useStateUserInfo } from "@/hooks/user.js";
import { useTranslation } from "react-i18next";

import { fetchAndStoreAppList } from "@/utils/appListManager";

const AppItem = (props) => {
  const { t } = useTranslation();
  // eslint-disable-next-line react/prop-types
  const { title = "App Name", imgSrc = "" } = props;
  return (
    <Grid
      item
      sx={{
        width: pxToRem(98),
        marginRight: pxToRem(20),
      }}>
      <Grid
        sx={{
          width: pxToRem(98),
          height: pxToRem(98),
        }}>
        <img
          style={{
            width: "100%",
          }}
          src={imgSrc}></img>
      </Grid>
      <Grid
        sx={{
          textAlign: "center",
          font: `normal normal bold 16px/20px Proxima Nova`,
          color: "#000",
        }}>
        {title}
      </Grid>
    </Grid>
  );
};

const AppDetailsItem = (props) => {
  // eslint-disable-next-line react/prop-types
  const { title = "App Name", imgSrc = "", list = [] } = props;
  return (
    <Grid
      item
      xs={4}
      sx={{
        flexGrow: 1,
        minHeight: pxToRem(227),
        padding: pxToRem(12),
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}>
      <Grid
        sx={{
          display: "flex",
          alignItems: "center",

          p: 4,
        }}>
        <img
          style={{
            width: pxToRem(71),
          }}
          src={imgSrc}></img>
        <Grid
          sx={{
            width: pxToRem(94),
            marginLeft: pxToRem(12),
            color: "#ffffff",
            fontWeight: "600",
            font: `normal normal bold 25px/25px Proxima Nova`,
            textShadow: "0px 2px 4px #00000029",
          }}>
          {title}
        </Grid>
      </Grid>

      <Grid
        sx={{
          maxWidth: pxToRem(310),
          boxShadow: "0px 2px 4px #00000029",
          borderRadius: "5px",
          padding: pxToRem(20),
          opacity: 1,
          flexGrow: 1,
          marginTop: pxToRem(14),
          background: `#ecf6f3 0% 0% no-repeat padding-box`,
        }}>
        {list.map((item) => {
          return (
            <Grid
              sx={{
                marginBottom: pxToRem(8),
                display: "flex",
                alignItems: "center",
              }}
              key={item}>
              {/* <CheckIcon sx={{ color: "RGB(78,181,61)" }}></CheckIcon> */}
              <img
                src={CheckIcon}
                alt="加载失败"
                style={{
                  width: pxToRem(20),
                  height: pxToRem(20),
                }}
              />
              <Grid
                sx={{
                  font: `normal normal normal 14px/22px Proxima Nova`,
                  marginLeft: pxToRem(8),
                }}>
                {item}
              </Grid>
            </Grid>
          );
        })}
      </Grid>
    </Grid>
  );
};

const Home = () => {
  const { t } = useTranslation();
  const resData = useStateUserInfo();

  const [useAppList, setUseAppList] = useState([]);

  useEffect(() => {
    // 获取应用列表并存储到Redux
    fetchAndStoreAppList()
      .then((appList) => {
        setUseAppList(appList);
      })
      .catch((error) => {
        console.error("获取应用列表失败:", error);
        setUseAppList([]);
      });
  }, []);

  const screenList = [
    t("home.screenList_item_0"),
    t("home.screenList_item_1"),
    t("home.screenList_item_2"),
  ];

  const nuTagList = [
    t("home.nuTagList_item_1"),
    t("home.nuTagList_item_2"),
    t("home.nuTagList_item_3"),
    t("home.nuTagList_item_4"),
    t("home.nuTagList_item_5"),
  ];

  const zataList = [
    t("home.zataList_item_1"),
    t("home.zataList_item_2"),
    t("home.zataList_item_3"),
    t("home.zataList_item_4"),
    t("home.zataList_item_5"),
  ];

  const linkList = [
    {
      icon: instagram,
      url: "instagram",
    },
    {
      icon: linkedin,
      url: "linkedin",
    },
    {
      icon: videoIcon,
      url: "videoIcon",
    },
    {
      icon: facebook,
      url: "facebook",
    },
  ];

  return (
    <Grid>
      <Grid
        sx={{
          boxShadow: "0px 2px 6px #00000029",
          borderRadius: pxToRem(10),
          opacity: 1,
          backgroundColor: "#FFFFFF",
          minHeight: pxToRem(95),
          display: "flex",
          alignItems: "center",
        }}>
        <Grid
          sx={{
            font: `normal normal bold 24px/28px Proxima Nova`,
            display: "flex",
            alignItems: "center",
            padding: "20px",
            fontWeight: "bold",
          }}>
          {`${t("home.welcome")}, ${resData?.firstName} ${
            resData?.lastName ? `${resData.lastName}` : ""
          }!`}
        </Grid>
      </Grid>
      <Grid
        sx={{
          marginTop: pxToRem(20),
          display: "flex",
        }}>
        <Grid
          sx={{
            width: pxToRem(450),
            minHeight: pxToRem(160),
          }}>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
              background: `#F3F3F3 0% 0% no-repeat padding-box`,
              borderRadius: "10px",
            }}>
            <Grid
              sx={{
                width: pxToRem(120),
                height: pxToRem(120),
                m: 2,
              }}>
              <img
                style={{
                  width: "100%",
                  height: "100%",
                  borderRadius: "50%",
                }}
                src={resData?.photo || user}></img>
            </Grid>
            <Grid ml={2}>
              <Grid
                sx={{
                  font: `normal normal bold 24px/28px Proxima Nova`,
                  color: "#222222",
                }}>
                {resData?.firstName} {resData?.lastName}
              </Grid>
              <Grid
                sx={{
                  font: `normal normal normal 16px/22px Proxima Nova`,
                  color: " #000000",
                  mt: 1,
                }}>
                {resData?.email}
              </Grid>
            </Grid>
          </Grid>
          {resData?.employeeType == "2" && (
            <Grid
              sx={{
                marginTop: pxToRem(20),
              }}>
              <Grid
                sx={{
                  marginBottom: pxToRem(8),
                  fontSize: pxToRem(20),
                }}>
                {t("home.your_app")}
              </Grid>
              <Grid container>
                {useAppList?.find((item) => item.code == "SD") &&
                  useAppList?.find((item) => item.code == "SD")?.isSubscribed ==
                    "Y" && (
                    <AppItem
                      imgSrc={Screen_Direct}
                      title={"Screen Direct"}></AppItem>
                  )}

                {useAppList?.find((item) => item.code == "NT") &&
                  useAppList?.find((item) => item.code == "NT")?.isSubscribed ==
                    "Y" && <AppItem imgSrc={NuTag} title={"Nutag"}></AppItem>}

                {useAppList?.find((item) => item.code == "ZT") &&
                  useAppList?.find((item) => item.code == "ZT")?.isSubscribed ==
                    "Y" && <AppItem imgSrc={Zata} title={"Zata"}></AppItem>}
              </Grid>
            </Grid>
          )}
          {resData?.employeeType == "2" ? (
            <Grid
              sx={{
                marginTop: pxToRem(30),
              }}>
              <Grid
                sx={{
                  marginBottom: pxToRem(8),
                  fontSize: pxToRem(20),
                }}>
                {t("home.other_app")}
              </Grid>
              <Grid container>
                {useAppList?.find((item) => item.code == "SD") &&
                  useAppList?.find((item) => item.code == "SD")?.isSubscribed ==
                    "N" && (
                    <AppItem
                      imgSrc={Screen_Direct}
                      title={"Screen Direct"}></AppItem>
                  )}

                {useAppList?.find((item) => item.code == "NT") &&
                  useAppList?.find((item) => item.code == "NT")?.isSubscribed ==
                    "N" && <AppItem imgSrc={NuTag} title={"Nutag"}></AppItem>}

                {useAppList?.find((item) => item.code == "ZT") &&
                  useAppList?.find((item) => item.code == "ZT")?.isSubscribed ==
                    "N" && <AppItem imgSrc={Zata} title={"Zata"}></AppItem>}
              </Grid>
            </Grid>
          ) : (
            <Grid
              sx={{
                marginTop: pxToRem(30),
              }}>
              <Grid
                sx={{
                  marginBottom: pxToRem(8),
                  fontSize: pxToRem(20),
                }}>
                {t("home.other_app")}
              </Grid>
              <Grid container>
                <AppItem
                  imgSrc={Screen_Direct}
                  title={"Screen Direct"}></AppItem>

                <AppItem imgSrc={NuTag} title={"Nutag"}></AppItem>

                <AppItem imgSrc={Zata} title={"Zata"}></AppItem>
              </Grid>
            </Grid>
          )}
        </Grid>

        <Grid
          sx={{
            flexGrow: 1,
            ml: 4,
          }}>
          <Grid
            sx={{
              minHeight: pxToRem(425),
              background: "linear-gradient(164deg, #78BC27, #1487CB)",
              boxShadow: "0px 2px 4px #00000029",
              borderRadius: pxToRem(10),
              opacity: 1,
              paddingBottom: pxToRem(20),
            }}>
            <Grid container>
              <AppDetailsItem
                title={"Screen Direct"}
                list={screenList}
                imgSrc={Screen_Direct1}></AppDetailsItem>
              <AppDetailsItem
                title={"NuTag"}
                list={nuTagList}
                imgSrc={NuTag1}></AppDetailsItem>
              <AppDetailsItem
                title={"Zata"}
                list={zataList}
                imgSrc={Zata1}></AppDetailsItem>
            </Grid>
          </Grid>

          <Grid
            sx={{
              display: "flex",
              marginTop: pxToRem(15),
              flexGrow: 1,
            }}>
            <Grid
              sx={{
                width: "135px",
                height: "288px",
                position: "relative",
              }}>
              <img
                style={{
                  position: "absolute",
                  // top: pxToRem(-100),
                  // left: pxToRem(-133),
                  // width: pxToRem(269),
                  // height: pxToRem(338),
                }}
                src={PhoneblueESL}></img>
            </Grid>
            <Grid
              sx={{
                width: pxToRem(332),
                height: pxToRem(288),
                borderRadius: pxToRem(10),
                padding: `${pxToRem(20)} ${pxToRem(30)}`,
                opacity: 1,
                background: "linear-gradient(153deg, #FF8032, #D96117)",
              }}>
              <Grid
                sx={{
                  fontSize: pxToRem(24),
                  color: "#ffffff",
                  textAlign: "center",
                }}>
                {t("home.download_mobile")}
              </Grid>
              <Grid
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: pxToRem(12),
                }}>
                <img
                  style={{
                    width: pxToRem(234),
                  }}
                  src={pngegg1}></img>
              </Grid>
              <Grid
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: pxToRem(12),
                }}>
                <img
                  style={{
                    width: pxToRem(234),
                  }}
                  src={pngegg}></img>
              </Grid>
            </Grid>
            <Grid
              sx={{
                marginLeft: pxToRem(18),
                borderRadius: pxToRem(10),
                boxShadow: "0px 0px 10px #0000000D",
                background: "linear-gradient(170deg, #5727C5, #3B0EA2)",
                display: "flex",
                flexGrow: 1,
              }}>
              <Grid
                sx={{
                  position: "relative",
                  width: pxToRem(288),
                  height: pxToRem(288),
                }}>
                <img
                  style={{
                    width: pxToRem(288),
                    height: pxToRem(288),
                  }}
                  src={MaskGroup}></img>
                <img
                  style={{
                    position: "absolute",
                    width: pxToRem(66),
                    height: pxToRem(69),
                    top: "40%",
                    right: pxToRem(25),
                  }}
                  src={notif}></img>
              </Grid>
              <Grid
                sx={{
                  flexGrow: 1,
                }}>
                <Grid
                  sx={{
                    marginLeft: "-40px",
                  }}>
                  <Grid
                    sx={{
                      height: pxToRem(104),
                      position: "relative",
                      width: "100%",
                    }}>
                    <img
                      style={{
                        height: pxToRem(104),
                      }}
                      src={notif2}></img>

                    <img
                      style={{
                        height: pxToRem(104),
                        position: "absolute",
                        right: pxToRem(40),
                        top: pxToRem(40),
                      }}
                      src={Copy}></img>
                  </Grid>
                  <Grid>
                    <Grid
                      sx={{
                        color: "#ffffff",
                        width: pxToRem(185),
                        fontSize: pxToRem(28),
                        marginLeft: pxToRem(38),
                      }}>
                      {t("home.find_madia")}
                    </Grid>

                    <Grid
                      sx={{
                        display: "flex",
                        marginLeft: pxToRem(30),
                        marginTop: pxToRem(10),
                      }}>
                      {linkList.map((item, index) => {
                        return (
                          <Grid key={"key" + index}>
                            <img
                              style={{
                                width: pxToRem(60),
                                height: pxToRem(60),
                              }}
                              src={item.icon}></img>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};
export default Home;
