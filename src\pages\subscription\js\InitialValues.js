import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
export const getInitialValues = (state) => {
  if (state?.type == "add") {
    return {
      address: "",
      deviceCount: "",
      activateTime: "",
      expiraTime: "",
      departmentName: "",
      contactEmail: "",
      //  password: "",
      contactPhone: "",
      contractNo: "",
      contractAmount: "",
      contractAmountUnit: "",
      unit: "1",
      count: "",
    };
  } else {
    return {
      id: state?.id,
      address: state?.data?.address,
      deviceCount: state?.data?.deviceCount,
      activateTime: state?.data?.activateTime,
      expiraTime: state?.data?.expiraTime,
      departmentName: state?.data?.departmentName,
      contactEmail: state?.data?.accountEmail,
      contactPhone: state?.data?.contactPhone,
      contractNo: state?.data?.contractNo,
      contractAmount: state?.data?.contractAmount,
      contractAmountUnit: state?.data?.contractAmountUnit,
      packageId: state?.type === "1" ? undefined : state?.data?.packageId,
      countryCode: state?.data?.countryCode,
      contactPhone: state?.data?.contactPhone,
      unit: state?.data?.unit || "1",
      departmentId: state?.data?.departmentId,
      count: state?.data?.count
    };
  }
};
