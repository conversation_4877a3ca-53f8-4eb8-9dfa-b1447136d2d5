import LoadingButton from "@mui/lab/LoadingButton";
import { Box, Button, Grid, Typography } from "@mui/material";
import React, { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

import KeyBoardLeftArrowIcon from "@/assets/Icons/KeyboardArrowLeftIcon.svg?react";
import EditorIcon from "@/assets/Icons/EditIcon.svg?react";
import AnimateButton from "@c/@extended/AnimateButton";
import CustomInput from "@c/CustInput";
import SvgIcon from "@c/SvgIcon";
import ZKSelect from "@c/ZKSelect";
import { PartnerColumns, RetailColumns } from "./components/Colums";
import DataTable from "./components/DataTable";
import Principals from "./principals/Index";
import { getCurrentPageData } from "./components/utils";
import ZKSearch from "./components/ZKSearch";
import { useStateUserInfo } from "@/hooks/user";
import {
  addDataPermission,
  editDataPermission,
  getDataDetail,
} from "@/service/api/dataPermission.js";

function AddDataPermission() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();

  // 零售商 或 代理商  实例
  const principalRef = useRef(null);

  // 数据权限名称
  const [name, setName] = useState("");
  // 名称字段错误信息
  const [nameError, setNameError] = useState("");
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 数据类型（4: 全部数据, 5: 自定义代理商权限, 6: 自定义零售客户权限）
  const [dataType, setDataType] = useState("4");
  // 是否打开选择负责人对话框
  const [principalsOpen, setPrincipalsOpen] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 是否为编辑模式的初始化状态
  const [editorInit, setEditorInit] = useState(false);
  // 编辑模式下的ID列表
  const [editorIds, setEditorIds] = useState([]);
  // 是否用户操作
  const [isUserAction, setIsUserAction] = useState(false);

  // 新增是数据id
  const [addDataIds, setAddDataIds] = useState([]);

  useEffect(() => {
    if (editorInit) return;
    if (dataType == "5" || dataType == "6") {
      setPrincipalsOpen(true);
      setData([]);
    }
  }, [dataType]);

  // 获取编辑数据
  useEffect(() => {
    if (state?.type == "editor") {
      getDataDetail(state?.id).then((res) => {
        const { name, type, outletList, departmentIds, departmentList } =
          res?.data;
        setName(name);
        setDataType(type);
        setEditorInit(true);

        const list =
          type == "6" ? outletList?.map((x) => x.principalId) : departmentIds;
        const set = new Set([...list, ...departmentIds]);

        setEditorIds(Array.from(set));
        setAddDataIds(departmentIds);

        let processedList;
        if (type == "6") {
          processedList = outletList.map((item) => ({
            ...item,
            id: item.principalId,
            name: item.principalName,
            email: item.principalEmail,
            areaName: item.areaName,
          }));
        } else {
          processedList = departmentList;
        }

        setData(processedList);
        principalRef.current.setSelectedRows(processedList, true);
      });
    }
  }, []);

  // 校验名称字段
  const validateName = (value) => {
    if (!value || value.trim() === "") {
      setNameError(t("data_permission.name_required"));
      return false;
    }
    setNameError("");
    return true;
  };

  const handlerSubmit = async () => {
    // 校验名称字段
    if (!validateName(name)) {
      return;
    }

    const params = {
      id: state?.id,
      name: name.trim(),
      type: dataType,
      departmentIds: addDataIds,
    };

    setLoading(true);
    try {
      const apiCall =
        state?.type == "editor" ? editDataPermission : addDataPermission;
      const res = await apiCall(params);
      navigate("/data/permission");
      toast.success(res.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <React.Fragment>
      <Grid
        sx={{
          width: "100%",
          height: "100%",
          backgroundColor: "#FFF",
          overflowY: "auto",
        }}>
        <TitleHeader state={state}></TitleHeader>
        <PermissionType
          name={name}
          state={state}
          setName={setName}
          nameError={nameError}
          setNameError={setNameError}
          validateName={validateName}
          dataType={dataType}
          setIsUserAction={setIsUserAction}
          setDataType={setDataType}></PermissionType>

        {dataType == "4" ? (
          <BestPermission></BestPermission>
        ) : (
          <Grid
            sx={{
              borderTop: "1px solid #e8e8e8",
              p: 2,
            }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
              }}>
              <Typography
                style={{
                  marginBottom: "10px",
                  fontWeight: 600,
                  fontSize: "16px",
                }}>
                {dataType == "6"
                  ? t("data_permission.retail_client_info")
                  : t("data_permission.partner_info")}
              </Typography>

              <Box
                sx={{
                  width: "50px",
                  height: "50px",
                  color: "#Fff",
                  borderRadius: "4px",
                  justifyItems: "center",
                  alignContent: "center",

                  mr: 2,
                  "&:hover": {
                    backgroundColor: "#f0f0f0",
                  },
                }}
                onClick={() => setPrincipalsOpen(true)}>
                <EditorIcon></EditorIcon>
              </Box>
            </Box>

            <ZKSearch data={data} setData={setData}></ZKSearch>

            <TableList
              data={data}
              setData={setData}
              dataType={dataType}></TableList>
          </Grid>
        )}

        <Grid
          sx={{
            display: "flex",
            gap: 4,
            justifyContent: "flex-end",
            m: 2,
          }}>
          <Button
            style={{
              width: "182px",
              height: "60px",
              borderRadius: "10px",
              opacity: 1,
              border: "1px solid #E3E3E3",
            }}
            onClick={() => navigate("/data/permission")}>
            {t("common.common_op_return")}
          </Button>

          <AnimateButton>
            <LoadingButton
              loading={loading}
              disableElevation
              // disabled={formik?.isSubmitting}
              fullWidth
              type="submit"
              onClick={handlerSubmit}
              variant="contained"
              style={{
                width: "182px",
                height: "60px",
                borderRadius: "10px",
                opacity: 1,
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}>
              {t("common.common_save")}
            </LoadingButton>
          </AnimateButton>
        </Grid>
      </Grid>

      <Principals
        ref={principalRef}
        editorInit={editorInit}
        setEditorInit={setEditorInit}
        dataType={dataType}
        list={data}
        setList={setData}
        setAddDataIds={setAddDataIds}
        setEditorIds={setEditorIds}
        principalsOpen={principalsOpen}
        isUserAction={isUserAction}
        setIsUserAction={setIsUserAction}
        setPrincipalsOpen={setPrincipalsOpen}></Principals>
    </React.Fragment>
  );
}

export default AddDataPermission;

const PermissionType = (props) => {
  const {
    name,
    setName,
    nameError,
    setNameError,
    validateName,
    dataType,
    setDataType,
    state,
    setIsUserAction,
  } = props;
  const { t } = useTranslation();
  const useInfo = useStateUserInfo();

  const dataTypeOptions = [
    { label: t("datascope.all_data"), value: "4" },
    useInfo?.employeeType == "0" && {
      label: t("datascope.custom_tip"),
      value: "5",
    },
    { label: t("datascope.custom_retail"), value: "6" },
  ];

  return (
    <Grid
      container
      spacing={4}
      sx={{
        p: 2,
        width: "50%",
      }}>
      <Grid item xs={6}>
        <CustomInput
          label={t("data_permission.name")}
          name="name"
          required
          value={name}
          handleChange={(e) => {
            setName(e.target.value);
            // 清除错误信息当用户开始输入时
            if (nameError) {
              setNameError("");
            }
          }}
          handleBlur={() => {
            // 失焦时进行校验
            validateName(name);
          }}
          placeholder={t("data_permission.enter_name")}
          error={nameError} // CustomInput组件使用error属性显示错误信息
        />
      </Grid>

      <Grid item xs={6}>
        <ZKSelect
          label={t("data_permission.type")}
          placeholder={t("data_permission.select_type")}
          options={dataTypeOptions}
          id="dataType"
          fullWidth
          name="dataType"
          disabled={state?.type == "editor"}
          value={dataType}
          isClear={false}
          onChange={(e) => {
            setIsUserAction(true);
            setDataType(e.target.value);
          }}
          sx={{
            "&.MuiInputBase-root": {
              height: "52px",
              color: "#474B4F",
              // boxShadow: "0px 1px 3px #0000001A",
              borderRadius: "8px",
            },
          }}
        />
      </Grid>
    </Grid>
  );
};

const TitleHeader = (props) => {
  const { state } = props;
  const navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <Tooltip
      title={t("返回")}
      style={{
        cursor: "pointer",
        display: "flex",
        margin: "10px",
        alignItems: "center",
      }}>
      <Grid
        item
        xs={12}
        onClick={() => {
          navigate("/data/permission");
        }}
        sx={{
          height: "80px",
          borderBottom: "1px solid #e8e8e8",
        }}>
        <KeyBoardLeftArrowIcon></KeyBoardLeftArrowIcon>

        <Typography
          style={{
            font: `normal normal bold 20px/24px Roboto`,
            color: "#222222",
            opacity: 0.8,
            marginLeft: "15px",
          }}>
          {state?.type == "editor"
            ? t("data_permission.edit_title")
            : t("data_permission.add_title")}
        </Typography>
      </Grid>
    </Tooltip>
  );
};

const TableList = (props) => {
  const { data, dataType } = props;

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  return (
    <Grid
      sx={{
        maxHeight: "430px",
        overflowY: "auto",
      }}>
      <DataTable
        columns={dataType == "6" ? RetailColumns() : PartnerColumns()}
        data={getCurrentPageData(data, pagination)}
        rowCount={data?.length}
        rowsPerPage={pagination.pageSize}
        currentPage={pagination.pageIndex}
        enableRowActions={false}
        getRowId={(originalRow) =>
          originalRow?.principalId + originalRow?.outletId
        } // 确保 ID 唯一
        onPageChange={(pageIndex) => {
          setPagination((prev) => ({
            ...prev,
            pageIndex, // 更新页码
          }));
        }}
        onPageSizeChange={(pageSize) => {
          setPagination({
            pageIndex: 0, // 重置页码
            pageSize, // 更新每页行数
          });
        }}></DataTable>
    </Grid>
  );
};

const BestPermission = () => {
  const { t } = useTranslation();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "60%",
        background: `#F6F7FB 0% 0% no-repeat padding-box`,
        position: "relative",
      }}>
      <SvgIcon height="10em" width="10em" localIcon="NoResultFound"></SvgIcon>
      <span>{t("datascope.currently_highest")}</span>
      <span>{t("datascope.all_data_tip")}</span>
    </div>
  );
};
