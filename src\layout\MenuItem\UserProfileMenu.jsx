import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import { toast } from "react-toastify";
import { Typography, CardContent, Box } from "@mui/material";
import { Person } from "@mui/icons-material";
// 组件
import CustomInput from "@c/CustInput.jsx";
import CustomePhoneFiled from "@c/CustomePhoneFiled";
import SubmitButton from "@c/SubmitButton";
import AvatarUploader from "@/components/AvatarUploader";

import { changeUserInfo } from "@/service/api/user.js";
import { useStateUserInfo } from "@/hooks/user.js";

function UserProfileMenu() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);
  const [isRemove, setIsRemove] = useState("1");

  // 处理头像上传
  const handleUpload = (file) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // 加载用户数据
  useEffect(() => {
    if (userInfo) {
      formik.setFieldValue("firstName", userInfo.firstName || "");
      formik.setFieldValue("lastName", userInfo.lastName || "");
      formik.setFieldValue("email", userInfo.email || "");
      formik.setFieldValue("countryCode", userInfo.countryCode || "");
      formik.setFieldValue("phone", userInfo.phone || "");
      setImageUrl(userInfo.photo || "");
    }
  }, [userInfo]);

  // 表单处理
  const formik = useFormik({
    initialValues: {
      email: "",
      firstName: "",
      lastName: "",
      countryCode: "",
      phone: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append("multipartFile", fileUrl);
        formData.append("firstName", values.firstName);
        formData.append("lastName", values.lastName);
        formData.append("email", values.email);
        formData.append("countryCode", values.countryCode);
        formData.append("phone", values.phone);
        formData.append("isRemove", isRemove);

        const res = await changeUserInfo(formData);
        toast.success(res?.message);
        window.location.reload();
      } finally {
        setStatus({ success: false });
        setLoading(false);
        setSubmitting(false);
      }
    },
  });

  // 处理头像移除
  const handleRemove = () => {
    setIsRemove("0");
    setImageUrl(null);
    setFileUrl(null);
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        gap: 2,
      }}>
      {/* 页面标题 */}
      <Card
        sx={{
          position: "sticky",
          top: 0,
          zIndex: 50,
          height: 80,
          display: "flex",
          alignItems: "center",
          borderRadius: 3,
          boxShadow: 1,
        }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="600">
            {t("common.my_profile")}
          </Typography>
        </CardContent>
      </Card>

      {/* 主内容区 */}
      <Card sx={{ flex: 1, borderRadius: 3, boxShadow: 2, overflowY: "auto" }}>
        <CardContent sx={{ p: 0 }}>
          <form noValidate onSubmit={formik.handleSubmit}>
            <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
              <AvatarUploader
                imageUrl={imageUrl}
                setImageUrl={setImageUrl}
                onRemove={handleRemove}
                handleUpload={handleUpload}
              />
            </Box>
            {/* 基本信息区 */}
            <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
              <Box display="flex" alignItems="center" gap={1} mb={3}>
                <Person color="primary" />
                <Typography variant="h5" fontWeight="600">
                  {t("common.common_base_info")}
                </Typography>
              </Box>

              <Grid container xs={12} spacing={4}>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="firstName"
                    formik={formik}
                    label={t("branch_user.firstName")}
                    placeholder={t("branch_user.enter_firstName")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="lastName"
                    formik={formik}
                    label={t("branch_user.lastName")}
                    placeholder={t("branch_user.enter_lastName")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomInput
                    required
                    name="email"
                    formik={formik}
                    label={t("common.common_email")}
                    disabled={true}
                    placeholder={t("common.enter_email")}
                  />
                </Grid>
                <Grid item xs={6}>
                  <CustomePhoneFiled
                    label={t("common.common_mobile")}
                    placeholder={t("common.enter_mobile")}
                    name="phone"
                    size="small"
                    className="bg-gray-50"
                    formik={formik}
                    required
                    disabled={true}
                    countryCode={formik.values.countryCode}
                    value={formik.values.phone}
                    handleCountryCode={(e) =>
                      formik.setFieldValue("countryCode", e.dialCode)
                    }
                    handleChange={(e) =>
                      formik.setFieldValue("phone", e.target.value)
                    }
                  />
                </Grid>
              </Grid>
            </Box>

            <Box
              sx={{
                px: 3,
                py: 2,
                mt: 10,
                alignItems: "flex-end",
                display: "flex",
                justifyContent: "flex-end",
              }}>
              <SubmitButton
                formik={formik}
                loading={loading}
                callbackRoute={-1}
              />
            </Box>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
}

export default UserProfileMenu;
