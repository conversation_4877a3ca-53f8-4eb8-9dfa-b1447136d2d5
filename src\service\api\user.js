import request from "@/utils/request";

export const getCaptcha = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/captcha/login/code`,
    method: "GET",
    headers: {
      isToken: false,
    },
  });
};
export const getCaptchaConfig = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/captcha/config`,
    method: "GET",
    headers: {
      isToken: false,
    },
  });
};

export const userLogin = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/login`,
    method: "post",
    data: params,
    headers: {
      isToken: false,
      isEncrypt: "true",
    },
  });
};

/**
 *  获取最新的token信息
 *
 * <AUTHOR>
 */
export const doRefreshToken = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/doRefreshToken`,
    method: "get",
  });
};

/**
 * 获取登录的用户信息
 * @param {} params
 */
export const getLoginInfor = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/user_info`,
    method: "GET",
  });
};

/**
 * 获取用户菜单树
 */

export const getUerMenuTree = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/resource/tree`,
    method: "GET",
    data: params,
  });
};

/**
 * 修改密码
 */

export const changeUserPassword = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/profile/password`,
    method: "PUT",
    data: data,
  });
};

/**
 * 修改用户信息
 */

export const changeUserInfo = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/profile`,
    method: "PUT",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

/**
 *     退出登录
 */

export const logout = () => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/logout`,
    method: "DELETE",
  });
};

/**
 *         发送验证码
 */

export const sendVerifyCode = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/captcha/forgot/code`,
    method: "GET",
    params: params,
  });
};

/**
 *         忘记密码
 */

export const forgetPassword = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/forget/password`,
    method: "PUT",
    data: data,
  });
};

/**
 *  获取按钮级权限
 */

export const getAuthButton = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/application/${params}`,
    method: "POST",
  });
};

/**
 *  验证旧密码
 */
export const verifyPassword = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/profile/password/verify`,
    method: "POST",
    data: data,
  });
};


/**
 *   注销账户
 */


export const closeAccount = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/unregister`,
    method: "POST",
    data: data,
  });
};




/**
 *    双因子 登录  验证密码
 * 
 */


export const validTfaPassword = (params) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/valid/tfa`,
    method: "GET",
    params: params,
  });
}


/**
 *  
 *  双因子验证  重新发送验证码  
 * 
 */

export const resendValidCode = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/auth/resend/tfa`,
    method: "POST",
    data: data,
    headers: {
      isToken: false,
    },
  });
}



/**
 *  
 *   是否开启  双因子验证  
 * 
 */

export const switchTfa = (data) => {
  return request({
    url: `${import.meta.env.VITE_APICODE}/user/tfa/switch`,
    method: "PUT",
    data: data
  });
}