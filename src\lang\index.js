import i18n from "i18next";
import enUsTrans from "./modules/en";
import zhCnTrans from "./modules/zh";
import esUsTrans from "./modules/es";
import esJpTrans from "./modules/jp";
import ptTrans from "./modules/pt";
import { initReactI18next } from "react-i18next";
// utils
import { getStoreLang } from "@/utils/langUtils";
i18n.use(initReactI18next).init({
  resources: {
    en: {
      translation: enUsTrans,
    },
    zh: {
      translation: zhCnTrans,
    },
    es: {
      translation: esUsTrans,
    },
    jp: {
      translation: esJpTrans,
    },
    pt: {
      translation: ptTrans,
    },
  },
  // 选择默认语言，选择内容为上述配置中的 key，即 en/zh
  fallbackLng: getStoreLang(),
  debug: false,
  interpolation: {
    escapeValue: false, // not needed for react as it escapes by default
  },
});

// 将 i18n 实例挂载到全局，供子应用使用
window.i18n = i18n;

export default i18n;
