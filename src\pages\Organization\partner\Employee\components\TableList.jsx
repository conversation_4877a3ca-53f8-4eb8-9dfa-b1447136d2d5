import React from "react";
import ZktecoTable from "@c/ZktecoTable";
import { useNavigate } from "react-router-dom";
import { useConfirm } from "@/components/zkconfirm";
import { toast } from "react-toastify";
import { deleteParnerUser } from "@s/api/partner";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  setOpen,
  setTenantId,
  getTableData,
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const confirmFn = useConfirm();
  const columns = useMemo(
    () => [
      {
        accessorKey: "logo",
        header: t("common.common_company_logo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Avatar
            src={row.original.photo}
            alt={"Logo"}
            size="medium"
            sx={{
              width: "121px",
              height: "50px",
              borderRadius: "8px",
              border: "1px solid #E3E3E3",
            }}
          />
        ),
      },
      {
        accessorKey: "firstName",
        header: t("branch_user.firstName"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "lastName",
        header: t("branch_user.lastName"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      {
        accessorKey: "email",
        header: t("branch.branch_email"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "phone",
        header: t("branch_user.phone"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          const countryCode = row.original.countryCode || "";
          const phone = row.original.phone || "";
          return `+${countryCode} ${phone}`.trim();
        },
      },

      {
        accessorKey: "roleNames",
        header: t("roles.title"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      // {
      //   accessorKey: "dataScope",
      //   header: t("data_permission.add_permission"),
      //   enableColumnActions: false,
      //   enableClickToCopy: false, // 启用点击复制
      //   enableSorting: false,
      //   size: 250,
      // },
    ],
    []
  );

  const isShowAction = {
    isShowView: "org:partner_employee:list",
    isShowEditor: "org:partner_employee:update",
    isShowDetele: "org:partner_employee:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  // 删除
  const handlerDetele = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("branch.delete_content"),
    }).then(() => {
      deleteParnerUser(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/partner/view/employee", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/partner/add/employee", {
          state: { id: data?.id, type: "editor" },
        }),

      Detele: (data) => {
        handlerDetele(data);
      },
    }),
    [navigate, setOpen, setTenantId]
  );

  return (
    <React.Fragment>
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        pathRoute="/partner/add/employee"
        loadDada={getTableData}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "org:partner_employee:save",
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    </React.Fragment>
  );
}

export default TableList;
