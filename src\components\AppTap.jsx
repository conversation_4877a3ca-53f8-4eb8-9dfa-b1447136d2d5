import React from "react";
import { getAppList } from "@/service/api/common.js";
import ZKDIGIMAXLOGO from "@/assets/Images/Logo/ZKDIGIMAX.png";
import { pxToRem } from "@u/zkUtils";
import SvgIcon from "@c/SvgIcon.jsx";
function AppTap(props) {
  const { value, setValue, isShowL3 } = props;

  const [appList, setAppList] = useState([]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getAppList();
        const imageMapping = {
          L3: "ZKDIGIMAXLOGO",
          SD: "ScreenDirectSVG",
          ZT: "ZataSVG",
          NT: "NuTagSVG",
        };

        // 根据 isShowL3 来决定是否过滤掉 code 为 "L3" 的对象
        const filteredData = isShowL3
          ? res?.data
          : res?.data.filter((item) => item.code !== "L3");

        // 为每个 item 添加 image 键值对
        const updatedData = filteredData.map((item) => {
          item.image = imageMapping[item.code] || null; // 如果没有匹配的 code，image 默认为 null
          return item;
        });

        // 设置更新后的数据
        setAppList(updatedData);
      } catch (error) {
        console.error("Error fetching app list:", error);
      }
    };

    fetchData();
  }, [isShowL3]);

  useEffect(() => {
    const storedAppType = sessionStorage.getItem("APP_TYPE");

    const initialValue = isShowL3
      ? storedAppType || "L3"
      : storedAppType === "L3"
      ? "SD"
      : storedAppType || "SD";
    if (storedAppType !== initialValue) {
      sessionStorage.setItem("APP_TYPE", initialValue);
    }

    setValue(initialValue);
  }, []);

  return (
    <React.Fragment>
      <Grid
        container
        sx={{
          background: "#fff",
          p: 3,
          borderRadius: "10px",
          flexDirection: "row",
          display: "flex",
        }}>
        {isShowL3 && (
          <TabButton
            image={"ZKDIGIMAXLOGO"}
            value={"L3"}
            isSelected={value == "L3"}
            onClick={() => {
              sessionStorage.setItem("APP_TYPE", "L3");
              setValue("L3");
            }}
          />
        )}

        {appList?.map((item, index) => {
          return (
            <Grid item key={item.id + index}>
              <TabButton
                image={item?.image}
                value={item?.code}
                title={item?.name}
                isSelected={value == item?.code}
                onClick={() => {
                  sessionStorage.setItem("APP_TYPE", item?.code);
                  setValue(item?.code);
                }}
              />
            </Grid>
          );
        })}
      </Grid>
    </React.Fragment>
  );
}

export default AppTap;

const TabButton = ({ image, title, value, isSelected, onClick }) => {
  return (
    <React.Fragment>
      <Grid
        item
        sx={{
          minWidth: pxToRem(250),
          height: pxToRem(60),
          border: isSelected ? "1px solid #78BC27" : "1px solid #E3E3E3",
          borderRadius: "10px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          textAlign: "center",
          cursor: "pointer",
          boxSizing: "border-box",
          whiteSpace: "collapse",
          ml: 2,
          "&:hover": {
            border: isSelected ? "1px solid #78BC27" : "1px solid #E3E3E3",
          },
        }}
        onClick={() => onClick(value)}>
        {image ? (
          image == "ZKDIGIMAXLOGO" ? (
            <img
              src={ZKDIGIMAXLOGO}
              style={{
                width: "158px",
                height: "32px",
              }}
              alt="加载失败"
            />
          ) : (
            <SvgIcon width="88px" height="100px" localIcon={image}></SvgIcon>
          )
        ) : (
          <Typography
            sx={{
              font: "normal normal bold 20px/24px Proxima Nova",
              color: "#474B4F",
            }}>
            {title}
          </Typography>
        )}
      </Grid>
    </React.Fragment>
  );
};
