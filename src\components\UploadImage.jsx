import React, { useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>rid,
  Stack,
  InputLabel,
  Avatar,
  Typography,
  Button,
} from "@mui/material";
import { toast } from "react-toastify";
import PlusIcon from "@a/Icons/Picture.svg?react";
import RequirePoint from "@c/RequirePoint";
import { pxToRem } from "@/utils/zkUtils.js";
function UploadImage(props) {
  const { t } = useTranslation();
  const {
    imageUrl,
    handleUpload,
    label,
    labelpostion,
    setImageUrl,
    name = "upload-image", // 提供默认值
  } = props;

  const fileInputRef = useRef();

  const handleAvatarClick = () => {
    fileInputRef.current.click(); // 触发隐藏的文件输入框
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (allowedTypes.includes(file.type)) {
        if (file.size <= maxSize) {
          handleUpload(file);
        } else {
          toast.error(t("File size exceeds 5MB limit"));
        }
      } else {
        // 显示错误消息
        toast.error(t("common.file_type_error"));
      }
    }
  };

  // Handle removing the uploaded image
  const handleRemoveImage = () => {
    try {
      setImageUrl(null);
      // Reset file input value if ref exists
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      toast.error(t("common.operation_failed") || "Failed to remove image");
    }
  };

  return (
    <React.Fragment>
      <Stack spacing={1} mt={4}>
        <Stack
          direction={labelpostion === "left" ? "row" : "column"}
          sx={{
            alignItems: labelpostion === "left" ? "flex-start" : "",
          }}
          spacing={1}>
          {label && (
            <InputLabel
              style={{
                marginTop: labelpostion === "left" ? "12px" : "",
                // color: "#474b4fcc",
                fontSize: "14px",
              }}
              htmlFor={"zkInput_" + name}>
              {label} {props.required && <RequirePoint></RequirePoint>}
            </InputLabel>
          )}
          <Grid
            container
            sx={{
              justifyContent: "start", // 水平居中
              alignItems: "center", // 垂直居中
              textAlign: "center",
              flexDirection: "row",
            }}>
            <Grid item>
              {imageUrl ? (
                <Avatar
                  className="avatar radial-button"
                  alt="加载失败"
                  sx={{ width: "200px", height: "200px", borderRadius: "10px" }}
                  src={imageUrl}
                  onClick={handleAvatarClick} // 点击时触发隐藏文件上传
                ></Avatar>
              ) : (
                <Grid
                  sx={{
                    width: "200px",
                    height: "200px",
                    border: "1px solid #E3E3E3",
                    borderRadius: "10px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  onClick={handleAvatarClick} // 点击时触发隐藏文件上传
                >
                  <PlusIcon
                    style={{
                      width: "70px",
                      height: "60px",
                      opacity: 0.5,
                    }}></PlusIcon>
                </Grid>
              )}

              <input
                type="file"
                accept=".jpg,.png,.jpeg"
                style={{ display: "none" }} // 隐藏文件输入框
                ref={fileInputRef} // 绑定引用
                onChange={handleFileChange} // 文件变化时触发
              />
            </Grid>

            <Grid
              item
              sx={{
                ml: 5,
              }}>
              <Typography
                sx={{
                  font: `normal normal normal 12px/14px Proxima Nova`,
                  color: "#474B4F",
                  opacity: 0.5,
                }}>
                {t("common.common_allowed")}
              </Typography>
              <Typography
                sx={{
                  font: `normal normal normal 12px/14px Proxima Nova`,
                  color: "#474B4F",
                  opacity: 0.5,
                  mt: 2,
                }}>
                {t("common.common_maximum")}
              </Typography>

              <Grid container mt={4}>
                <Button
                  sx={{
                    width: pxToRem(100),
                    height: pxToRem(36),
                    borderRadius: "5px",
                    color: "#FFFFFF",
                    font: `normal normal normal 14px/16px Proxima Nova`,
                    background:
                      "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                  }}
                  onClick={handleAvatarClick}>
                  {t("common.common_upload")}
                </Button>
                <Button
                  sx={{
                    width: pxToRem(100),
                    height: pxToRem(36),
                    border: "1px solid #E3E3E3",
                    borderRadius: "5px",
                    font: `normal normal normal 14px/16px Proxima Nova`,
                    color: "#000",
                    ml: 2,
                  }}
                  onClick={handleRemoveImage}>
                  {t("common.common_remove")}
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </Stack>
    </React.Fragment>
  );
}

export default UploadImage;
