{"name": "main-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "start": "vite --mode localhost", "build": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "node": "node distStart.js"}, "dependencies": {"@ant-design/colors": "^7.2.0", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@iconify/react": "^5.2.0", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^5.10.16", "@mui/lab": "5.0.0-alpha.100", "@mui/material": "^5.10.6", "@mui/x-date-pickers": "^7.27.1", "@mui/x-tree-view": "^7.23.2", "@reduxjs/toolkit": "^2.3.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.8", "bmap": "^1.0.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "express": "^4.16.3", "formik": "^2.4.6", "framer-motion": "^7.3.6", "hash-wasm": "^4.12.0", "http-proxy-middleware": "^0.18.0", "i18next": "^23.16.5", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "less": "^4.2.0", "lodash-es": "^4.17.21", "material-react-table": "^1.14.0", "material-ui-popup-state": "^5.3.1", "prop-types": "^15.8.1", "qiankun": "^2.10.16", "rc-tree-select": "^5.27.0", "react": "^18.3.1", "react-bmapgl": "^1.0.1", "react-redux": "^9.1.2", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-google-maps": "^9.4.5", "react-i18next": "^15.1.1", "react-measure": "^2.5.2", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.2", "react-window": "^1.8.10", "recompose": "^0.30.0", "redux": "^5.0.1", "simplebar-react": "^3.3.0", "tailwind-merge": "^3.2.0", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@mui/system": "^6.2.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss-pxtorem": "^6.1.0", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "3.4.17", "unplugin-auto-import": "^0.18.6", "unplugin-icons": "^22.0.0", "vite": "^5.4.10", "vite-plugin-html": "^3.2.2", "vite-plugin-restart": "^0.4.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0"}}