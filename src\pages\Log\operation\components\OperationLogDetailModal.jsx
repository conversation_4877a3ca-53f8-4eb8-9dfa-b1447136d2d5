import React, { useEffect, useRef, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Box,
  Typography,
  Divider,
  Paper,
  Tooltip,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import {
  Info as InfoIcon,
  Code as CodeIcon,
  Comment as CommentIcon,
  Error as ErrorIcon,
  CopyAll as CopyIcon,
} from "@mui/icons-material";
import { toast } from "react-toastify"; // 用于显示复制成功提示

import DictTag from "@/components/DictTag";
import {
  BootstrapActions,
  BootstrapContent,
  BootstrapDialog,
  BootstrapDialogTitle,
} from "@/components/dialog";

const OperationLogDetailModal = ({ open, onClose, logData, dictDataRef }) => {
  const { t } = useTranslation();

  const modalRef = useRef(null);

  // 获取模块名称
  const getModuleName = (code) => {
    const moduleKey = `operation_log.modules.${code}`;
    const translatedModule = t(moduleKey);
    // 如果翻译键不存在，返回原始代码
    return translatedModule !== moduleKey ? translatedModule : code;
  };

  // 获取业务类型名称
  const getBusinessTypeName = (type) => {
    const typeKey = `operation_log.business_types.${type}`;
    const translatedType = t(typeKey);
    // 如果翻译键不存在，返回默认格式
    return translatedType !== typeKey
      ? translatedType
      : `${t("operation_log.business_type_prefix")}${type}`;
  };

  // // 获取状态标签
  // const getStatusLabel = (status) => {
  //   if (status === 1) {
  //     return (
  //       <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
  //         成功
  //       </span>
  //     );
  //   }
  //   return (
  //     <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
  //       失败
  //     </span>
  //   );
  // };

  // 复制详情信息
  const copyDetailInfo = () => {
    if (!logData) return;

    let copyText = `${t("operation_log.copy_detail_title")}\n\n`;
    copyText += `${t("operation_log.copy_operator")} ${logData.operName}\n`;
    copyText += `${t("operation_log.copy_operation_time")} ${dayjs(
      logData.operationTime
    ).format("YYYY-MM-DD HH:mm")}\n`;
    copyText += `${t("operation_log.copy_operation_ip")} ${logData.ipAddr}\n`;
    copyText += `${t("operation_log.copy_operation_status")} ${
      logData.status === 1
        ? t("operation_log.status_success")
        : t("operation_log.status_failed")
    }\n`;
    copyText += `${t("operation_log.copy_request_url")} ${logData.url}\n`;
    copyText += `${t("operation_log.copy_request_params")} ${logData.param}\n`;
    copyText += `${t("operation_log.copy_operation_info")} ${t(
      logData.i18nName
    )}\n`;

    // 复制错误堆栈（如果有）
    if (logData.errorStack) {
      copyText += `${t("operation_log.copy_error_stack")} ${
        logData.errorStack
      }\n`;
    }

    navigator.clipboard
      .writeText(copyText)
      .then(() => {
        toast.success(t("operation_log.copy_success"), {
          position: toast.POSITION.BOTTOM_RIGHT,
          autoClose: 3000,
        });
      })
      .catch((err) => {
        toast.error(t("operation_log.copy_failed"), {
          position: toast.POSITION.BOTTOM_RIGHT,
          autoClose: 3000,
        });
        console.error("复制失败:", err);
      });
  };

  // 按ESC键关闭模态框
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape" && open) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [open, onClose]);

  if (!logData) return null;

  return (
    <BootstrapDialog
      ref={modalRef}
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        className: "max-h-[90vh] flex flex-col",
        sx: {
          borderRadius: 2,
          boxShadow: 8,
        },
      }}>
      <BootstrapDialogTitle
        onClose={onClose}
        className="flex items-center text-lg font-semibold">
        {/* <InfoIcon className="mr-2 text-primary" /> */}
        <Typography variant="h4">{t("operation_log.detail_title")}</Typography>
      </BootstrapDialogTitle>

      <BootstrapContent className="flex-grow overflow-y-auto p-0">
        <Box className="p-6">
          {/* 基本信息网格 */}
          <Box className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* 左侧信息 */}
            <Box className="space-y-4">
              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operator")}
                </Typography>
                <Tooltip title={logData.operName}>
                  <Typography variant="body1" noWrap={true}>
                    {logData.operName}
                  </Typography>
                </Tooltip>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operator_id")}
                </Typography>
                <Typography variant="body1">{logData.userId}</Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.department_info")}
                </Typography>
                <Typography variant="body1">
                  {logData.departmentId ? logData.departmentId : "-"} /
                  {logData.departmentCode ? logData.departmentCode : "-"}
                </Typography>
              </Box>

              {/* <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  租户编码
                </Typography>
                <Typography variant="body1">
                  {logData.tenantCode ? logData.tenantCode : "-"}
                </Typography>
              </Box> */}

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operation_time")}
                </Typography>
                <Typography variant="body1">
                  {dayjs(logData.operationTime).format("YYYY-MM-DD HH:mm")}
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.execute_time")}
                </Typography>
                <Typography variant="body1">
                  {logData?.executeTime}ms
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operation_status")}
                </Typography>
                <Typography variant="body1">
                  <DictTag
                    dicts={dictDataRef.current?.common_status}
                    value={logData.status}
                  />
                </Typography>
              </Box>
              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operation_type")}
                </Typography>
                <Typography variant="body1">
                  <DictTag
                    dicts={dictDataRef.current?.sys_operator_type}
                    value={logData.operatorType}
                  />
                </Typography>
              </Box>
            </Box>

            {/* 右侧信息 */}
            <Box className="space-y-4">
              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.host_ip")}
                </Typography>
                <Typography variant="body1">{logData.ipAddr}</Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.operation_location")}
                </Typography>
                <Tooltip title={logData.location}>
                  <Typography variant="body1" noWrap={true}>
                    {logData.location}
                  </Typography>
                </Tooltip>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.os_browser")}
                </Typography>
                <Typography variant="body1">
                  {logData.os} / {logData.browser}
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.request_url")}
                </Typography>
                <Tooltip title={logData.url}>
                  <Typography
                    variant="body1"
                    className="break-all"
                    noWrap={true}>
                    {logData.url}
                  </Typography>
                </Tooltip>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.request_method")}
                </Typography>
                <Typography variant="body1">{logData.requestMethod}</Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.app_module")}
                </Typography>
                <Typography variant="body1" noWrap={true}>
                  {logData.appCode} / {getModuleName(logData.moduleCode)}
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.business_type")}
                </Typography>
                <Typography variant="body1">
                  <DictTag
                    dicts={dictDataRef.current?.sys_operation_log_action}
                    value={logData.businessType}
                  />
                </Typography>
              </Box>

              <Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  className="mb-1">
                  {t("operation_log.method_name")}
                </Typography>
                <Tooltip title={logData.method}>
                  <Typography variant="body1" noWrap={true}>
                    {logData.method}
                  </Typography>
                </Tooltip>
              </Box>
            </Box>
          </Box>

          <Divider className="my-6" />

          {/* 详细信息区域 */}
          <Box className="space-y-6">
            {/* 操作信息 */}
            <Box>
              <Typography
                variant="subtitle2"
                className="flex items-center mb-3 text-gray-600">
                <CommentIcon className="mr-2 text-primary" />
                {t("operation_log.operation_info")}
              </Typography>
              <Paper variant="outlined" className="p-4 bg-gray-50">
                <Typography variant="body1">{t(logData.i18nName)}</Typography>
              </Paper>
            </Box>

            {/* 请求参数 */}
            <Box>
              <Typography
                variant="subtitle2"
                className="flex items-center mb-3 text-gray-600">
                <CodeIcon className="mr-2 text-primary" />
                {t("operation_log.request_params")}
              </Typography>
              <Paper
                variant="outlined"
                className="p-4 bg-gray-50 overflow-x-auto">
                <pre className="text-sm whitespace-pre-wrap">
                  {logData.param
                    ? JSON.stringify(JSON.parse(logData.param), null, 2)
                    : "-"}
                </pre>
              </Paper>
            </Box>

            {/* 错误堆栈信息 */}
            <Box>
              <Typography
                variant="subtitle2"
                className="flex items-center mb-3 text-gray-600">
                <ErrorIcon className="mr-2 text-red-500" />
                {t("operation_log.error_stack")}
                {!logData.errorStack && (
                  <Typography variant="caption" className="ml-2 text-gray-400">
                    {t("operation_log.no_error_info")}
                  </Typography>
                )}
              </Typography>
              <Paper
                variant="outlined"
                className="p-4 bg-gray-50 overflow-x-auto max-h-60">
                <pre
                  className={`text-sm whitespace-pre-wrap ${
                    logData.errorStack ? "text-red-800" : ""
                  }`}>
                  {logData.errorStack || t("operation_log.no_error_message")}
                </pre>
              </Paper>
            </Box>
          </Box>
        </Box>
      </BootstrapContent>

      <BootstrapActions className="p-4 border-t">
        <Button
          onClick={onClose}
          variant="outlined"
          className="w-[50px] rounded-md">
          {t("operation_log.close")}
        </Button>
      </BootstrapActions>
    </BootstrapDialog>
  );
};

export default OperationLogDetailModal;
