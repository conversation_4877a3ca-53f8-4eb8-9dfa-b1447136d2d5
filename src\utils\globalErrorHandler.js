/**
 * 全局错误处理器
 * 捕获未被ErrorBoundary捕获的错误，如模块加载错误、Promise rejection等
 */

// 错误类型枚举
export const ERROR_TYPES = {
  JAVASCRIPT_ERROR: 'javascript_error',
  PROMISE_REJECTION: 'promise_rejection',
  RESOURCE_ERROR: 'resource_error',
  CHUNK_LOAD_ERROR: 'chunk_load_error',
};

// 错误处理配置
const ERROR_CONFIG = {
  maxErrors: 10, // 最大错误数量
  timeWindow: 60000, // 时间窗口（毫秒）
  enableConsoleLog: process.env.NODE_ENV === 'development',
  enableErrorPage: true,
};

// 错误计数器
let errorCount = 0;
let errorTimestamp = Date.now();

// 错误队列
const errorQueue = [];

/**
 * 分析错误类型
 */
const analyzeError = (error, source) => {
  if (!error) return ERROR_TYPES.JAVASCRIPT_ERROR;

  const message = error.message || error.toString();
  const stack = error.stack || '';

  // Chunk加载错误
  if (message.includes('Loading chunk') || message.includes('ChunkLoadError')) {
    return ERROR_TYPES.CHUNK_LOAD_ERROR;
  }

  // 资源加载错误
  if (source && (source.includes('.js') || source.includes('.css') || source.includes('.png'))) {
    return ERROR_TYPES.RESOURCE_ERROR;
  }

  // Promise rejection
  if (error.name === 'UnhandledPromiseRejectionWarning') {
    return ERROR_TYPES.PROMISE_REJECTION;
  }

  // 语法错误检测 - 这些错误应该由ErrorBoundary处理，不跳转页面
  if (message.includes('Syntax') ||
    message.includes('Unexpected') ||
    message.includes('is not defined') ||
    error.name === 'SyntaxError' ||
    error.name === 'ReferenceError') {
    return ERROR_TYPES.JAVASCRIPT_ERROR; // 标记为普通JS错误，不跳转
  }

  return ERROR_TYPES.JAVASCRIPT_ERROR;
};

/**
 * 错误上报
 */
const reportError = (errorInfo) => {
  if (ERROR_CONFIG.enableConsoleLog) {
    // console.group('🚨 Global Error Caught');
    // console.error('Error Info:', errorInfo);
    // console.groupEnd();
  }

  // 添加到错误队列
  errorQueue.push({
    ...errorInfo,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  });

  // 保持队列大小
  if (errorQueue.length > 50) {
    errorQueue.shift();
  }

  // 生产环境发送到监控服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成Sentry、LogRocket等服务
    // sendToMonitoringService(errorInfo);
  }
};

/**
 * 检查是否需要显示错误页面
 */
const shouldShowErrorPage = (errorType, errorMessage = '') => {
  if (!ERROR_CONFIG.enableErrorPage) return false;

  // 绝对不允许JavaScript语法错误、引用错误跳转页面
  const isJavaScriptError =
    errorMessage?.includes('is not defined') ||
    errorMessage?.includes('not defined') ||
    errorMessage?.includes('Syntax') ||
    errorMessage?.includes('Unexpected') ||
    errorType === ERROR_TYPES.JAVASCRIPT_ERROR ||
    errorType === ERROR_TYPES.PROMISE_REJECTION;

  if (isJavaScriptError) {
    // console.warn('🚫 JavaScript错误绝不跳转页面，由ErrorBoundary处理');
    return false;
  }

  // 重置错误计数器（如果超过时间窗口）
  const now = Date.now();
  if (now - errorTimestamp > ERROR_CONFIG.timeWindow) {
    errorCount = 0;
    errorTimestamp = now;
  }

  errorCount++;

  // 只有真正的资源加载错误才跳转页面
  // 接口错误应该在request.js中单独处理
  // 网络错误不再自动跳转页面
  const criticalErrors = [
    ERROR_TYPES.CHUNK_LOAD_ERROR,
    ERROR_TYPES.RESOURCE_ERROR,
  ];

  return criticalErrors.includes(errorType);
};

/**
 * 跳转到错误页面
 */
const redirectToErrorPage = (errorType) => {
  // 绝对不允许JavaScript错误跳转到任何页面
  if (errorType === ERROR_TYPES.JAVASCRIPT_ERROR ||
    errorType === ERROR_TYPES.PROMISE_REJECTION ||
    errorType === ERROR_TYPES.REACT_ERROR) {
    // console.warn('🚫 JavaScript/React错误被阻止跳转页面，应该由ErrorBoundary处理');
    return;
  }

  // 检查是否为微前端相关路径
  const currentPath = window.location.pathname;
  const isMicroAppRoute =
    currentPath.startsWith('/cms-app') ||
    currentPath.startsWith('/retail-ai-app') ||
    currentPath.startsWith('/e-price-tag-app');

  // 如果是微前端路径，不进行跳转
  if (isMicroAppRoute) {
    // console.warn('🚫 微前端路径下的错误被阻止跳转页面:', currentPath);
    return;
  }

  // 检查是否在登录过程中或主页
  if (currentPath === '/login' || currentPath === '/' || currentPath.includes('/dashboard')) {

    return;
  }

  // 不再自动跳转到任何错误页面
  // Chunk加载错误也不跳转，让应用自己处理
  const errorPages = {};

  // 移除网络错误和服务器错误的自动跳转
  // 网络错误不再自动跳转页面
  // 服务器错误只由request.js处理

  const targetPage = errorPages[errorType];

  // 只有明确定义的错误类型才跳转
  if (!targetPage) {

    return;
  }

  // 避免无限重定向
  if (window.location.pathname !== targetPage) {

    window.location.href = targetPage;
  }
};

/**
 * JavaScript错误处理器
 */
const handleJavaScriptError = (event) => {
  const { message, filename, lineno, colno, error } = event;

  // 检查是否为微前端子应用的错误
  const currentPath = window.location.pathname;
  const isMicroAppRoute =
    currentPath.startsWith('/cms-app') ||
    currentPath.startsWith('/retail-ai-app') ||
    currentPath.startsWith('/e-price-tag-app');

  // 检查是否为微前端加载错误
  const isMicroAppLoadError =
    message?.includes('Loading chunk') ||
    message?.includes('qiankun') ||
    filename?.includes('qiankun') ||
    (isMicroAppRoute && (
      message?.includes('Script error') ||
      error?.name === 'ChunkLoadError'
    ));

  // 如果是微前端路由或微前端加载错误，不处理错误，让子应用自己处理
  if (isMicroAppRoute || isMicroAppLoadError) {
    if (ERROR_CONFIG.enableConsoleLog) {
      // console.warn('🔄 微前端相关错误，不跳转页面:', {
      //   path: currentPath,
      //   message: message,
      //   error: error,
      //   isMicroAppRoute,
      //   isMicroAppLoadError
      // });
    }
    return false; // 不处理，让子应用或qiankun处理
  }

  // 检测是否为语法错误或引用错误
  const isSyntaxOrReferenceError =
    message?.includes('Syntax') ||
    message?.includes('Unexpected') ||
    message?.includes('is not defined') ||
    message?.includes('not defined') ||
    error?.name === 'SyntaxError' ||
    error?.name === 'ReferenceError' ||
    error?.name === 'TypeError';

  const errorType = analyzeError(error, filename);

  const errorInfo = {
    type: errorType,
    message: message || 'Unknown JavaScript error',
    filename: filename || 'Unknown file',
    lineno: lineno || 0,
    colno: colno || 0,
    stack: error?.stack || 'No stack trace available',
    isSyntaxError: isSyntaxOrReferenceError,
  };

  reportError(errorInfo);

  // 所有JavaScript语法错误、引用错误、类型错误都不跳转页面
  // 完全由ErrorBoundary处理
  if (isSyntaxOrReferenceError || errorType === ERROR_TYPES.JAVASCRIPT_ERROR) {
    if (ERROR_CONFIG.enableConsoleLog) {
      // console.warn('🔍 JavaScript错误已记录，由ErrorBoundary处理，绝不跳转页面:', errorInfo);
    }
    return false; // 不阻止默认处理，让React的ErrorBoundary捕获
  }

  // 双重检查：使用shouldShowErrorPage再次确认是否需要跳转
  if (shouldShowErrorPage(errorType, message)) {
    if (ERROR_CONFIG.enableConsoleLog) {
      // console.error('🚨 关键错误，跳转到错误页面:', errorInfo);
    }
    redirectToErrorPage(errorType);
    return true; // 阻止默认错误处理
  }

  // 其他所有错误都不跳转页面
  if (ERROR_CONFIG.enableConsoleLog) {
    // console.warn('🔍 其他错误已记录，不跳转页面:', errorInfo);
  }
  return false;
};

/**
 * Promise rejection处理器
 */
const handlePromiseRejection = (event) => {
  const error = event.reason;
  const errorType = analyzeError(error);

  const errorInfo = {
    type: errorType,
    message: error?.message || error?.toString() || 'Unhandled Promise Rejection',
    stack: error?.stack || 'No stack trace available',
    promise: event.promise,
  };

  reportError(errorInfo);

  if (shouldShowErrorPage(errorType)) {
    redirectToErrorPage(errorType);
  }

  // 阻止默认处理
  event.preventDefault();
};

/**
 * 资源加载错误处理器
 */
const handleResourceError = (event) => {
  const target = event.target || event.srcElement;
  const errorType = ERROR_TYPES.RESOURCE_ERROR;

  const errorInfo = {
    type: errorType,
    message: `Failed to load resource: ${target.src || target.href || 'Unknown resource'}`,
    element: target.tagName,
    source: target.src || target.href,
  };

  reportError(errorInfo);

  if (shouldShowErrorPage(errorType)) {
    redirectToErrorPage(errorType);
  }
};

/**
 * 初始化全局错误处理器
 */
export const initGlobalErrorHandler = () => {
  // JavaScript错误
  window.addEventListener('error', handleJavaScriptError);

  // Promise rejection
  window.addEventListener('unhandledrejection', handlePromiseRejection);

  // 资源加载错误
  window.addEventListener('error', handleResourceError, true);

  if (ERROR_CONFIG.enableConsoleLog) {

  }
};

/**
 * 清理全局错误处理器
 */
export const cleanupGlobalErrorHandler = () => {
  window.removeEventListener('error', handleJavaScriptError);
  window.removeEventListener('unhandledrejection', handlePromiseRejection);
  window.removeEventListener('error', handleResourceError, true);
};

/**
 * 获取错误统计
 */
export const getErrorStats = () => {
  const stats = {
    totalErrors: errorQueue.length,
    errorsByType: {},
    recentErrors: errorQueue.slice(-10),
  };

  errorQueue.forEach(error => {
    stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1;
  });

  return stats;
};

/**
 * 手动触发错误页面（用于测试）
 */
export const triggerErrorPage = (errorType = ERROR_TYPES.JAVASCRIPT_ERROR) => {
  redirectToErrorPage(errorType);
};

// 默认导出
export default {
  initGlobalErrorHandler,
  cleanupGlobalErrorHandler,
  getErrorStats,
  triggerErrorPage,
  ERROR_TYPES,
};


