# 修改密码组件

一个基于 Material-UI 的通用修改密码组件，支持两步验证流程和密码强度检测。

## 功能特点

- ✅ **两步验证流程**：先验证当前密码，再设置新密码
- ✅ **密码强度检测**：实时显示密码强度和安全建议
- ✅ **完整表单验证**：包含所有必要的验证逻辑
- ✅ **Material-UI 风格**：完全符合 Material Design 规范
- ✅ **响应式设计**：适配各种屏幕尺寸
- ✅ **自定义配置**：支持自定义验证函数和标签文本
- ✅ **错误处理**：完善的错误提示和处理机制

## 组件结构

```
src/components/PasswordChange/
├── PasswordChangeDialog.jsx      # 主要的修改密码对话框组件
├── PasswordStrengthIndicator.jsx # 密码强度指示器组件
├── PasswordChangeDemo.jsx        # 演示页面
├── index.js                      # 入口文件
└── README.md                     # 说明文档
```

## 基础使用

```jsx
import React, { useState } from 'react';
import { Button } from '@mui/material';
import { PasswordChangeDialog } from './components/PasswordChange';

function App() {
  const [dialogOpen, setDialogOpen] = useState(false);

  // 验证当前密码的函数
  const validateCurrentPassword = async (password) => {
    // 调用后端API验证密码
    const response = await fetch('/api/validate-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ password })
    });
    return response.ok;
  };

  // 修改密码的函数
  const handlePasswordChange = async ({ currentPassword, newPassword }) => {
    // 调用后端API修改密码
    const response = await fetch('/api/change-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ currentPassword, newPassword })
    });
    
    if (!response.ok) {
      throw new Error('密码修改失败');
    }
  };

  return (
    <div>
      <Button onClick={() => setDialogOpen(true)}>
        修改密码
      </Button>
      
      <PasswordChangeDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        validateCurrentPassword={validateCurrentPassword}
        onPasswordChange={handlePasswordChange}
      />
    </div>
  );
}
```

## API 参考

### PasswordChangeDialog Props

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `open` | boolean | `false` | 控制对话框的显示/隐藏 |
| `onClose` | function | - | 关闭对话框的回调函数 |
| `validateCurrentPassword` | function | - | 验证当前密码的异步函数 |
| `onPasswordChange` | function | - | 修改密码的异步函数 |
| `title` | string | `'修改密码'` | 对话框标题 |
| `currentPasswordLabel` | string | `'当前密码'` | 当前密码输入框标签 |
| `newPasswordLabel` | string | `'新密码'` | 新密码输入框标签 |
| `confirmPasswordLabel` | string | `'确认新密码'` | 确认密码输入框标签 |

### PasswordStrengthIndicator Props

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `password` | string | `''` | 要检测的密码 |
| `showRequirements` | boolean | `true` | 是否显示密码要求列表 |
| `showScore` | boolean | `false` | 是否显示密码强度分数 |

## 密码强度规则

密码强度基于以下规则计算：

- **长度要求**：至少8位字符 (+20分)
- **小写字母**：包含小写字母 (+15分)
- **大写字母**：包含大写字母 (+15分)
- **数字**：包含数字 (+15分)
- **特殊字符**：包含特殊字符 (+15分)
- **长度奖励**：12位以上 (+10分)，16位以上 (+10分)

强度等级：
- **弱** (0-59分)：红色
- **中等** (60-79分)：橙色
- **强** (80-100分)：绿色

## 验证函数示例

### validateCurrentPassword

```jsx
const validateCurrentPassword = async (password) => {
  try {
    const response = await fetch('/api/auth/validate-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ password })
    });
    
    return response.ok;
  } catch (error) {
    console.error('密码验证失败:', error);
    return false;
  }
};
```

### onPasswordChange

```jsx
const handlePasswordChange = async ({ currentPassword, newPassword }) => {
  try {
    const response = await fetch('/api/auth/change-password', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        currentPassword,
        newPassword
      })
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '密码修改失败');
    }
    
    // 可选：修改成功后的处理
    localStorage.removeItem('token'); // 清除token
    window.location.href = '/login'; // 重定向到登录页
    
  } catch (error) {
    throw error; // 重新抛出错误，让组件处理
  }
};
```

## 自定义样式

组件使用 Material-UI 的 styled 系统，可以通过 theme 进行自定义：

```jsx
import { createTheme, ThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    success: {
      main: '#4caf50',
    },
    error: {
      main: '#f44336',
    },
    warning: {
      main: '#ff9800',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <PasswordChangeDialog {...props} />
    </ThemeProvider>
  );
}
```

## 错误处理

组件内置了完善的错误处理机制：

- **网络错误**：自动捕获并显示友好的错误信息
- **验证错误**：实时显示表单验证错误
- **服务器错误**：显示从后端返回的错误信息
- **超时处理**：支持请求超时处理

## 安全建议

1. **HTTPS传输**：确保在生产环境中使用HTTPS
2. **密码加密**：后端应对密码进行适当的加密存储
3. **会话管理**：密码修改后应使其他会话失效
4. **审计日志**：记录密码修改操作的审计日志
5. **频率限制**：实施密码修改频率限制

## 测试

演示页面提供了完整的测试环境：

- **测试密码**：`123456`
- **模拟延迟**：模拟真实的网络请求延迟
- **错误模拟**：10%的失败率用于测试错误处理

运行演示：

```jsx
import { PasswordChangeDemo } from './components/PasswordChange';

function App() {
  return <PasswordChangeDemo />;
}
```
