import { useState, useEffect, useCallback } from 'react';

/**
 * 加载状态管理 Hook
 * 提供统一的加载状态管理和进度控制
 */
const useLoadingState = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('正在加载...');
  const [progress, setProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);

  // 开始加载
  const startLoading = useCallback((message = '正在加载...', withProgress = false) => {
    setIsLoading(true);
    setLoadingMessage(message);
    setShowProgress(withProgress);
    setProgress(0);
  }, []);

  // 更新进度
  const updateProgress = useCallback((value, message) => {
    setProgress(Math.min(100, Math.max(0, value)));
    if (message) {
      setLoadingMessage(message);
    }
  }, []);

  // 更新消息
  const updateMessage = useCallback((message) => {
    setLoadingMessage(message);
  }, []);

  // 完成加载
  const finishLoading = useCallback(() => {
    if (showProgress) {
      setProgress(100);
      // 延迟隐藏，让用户看到完成状态
      setTimeout(() => {
        setIsLoading(false);
        setProgress(0);
        setShowProgress(false);
      }, 500);
    } else {
      setIsLoading(false);
    }
  }, [showProgress]);

  // 取消加载
  const cancelLoading = useCallback(() => {
    setIsLoading(false);
    setProgress(0);
    setShowProgress(false);
  }, []);

  // 模拟渐进式加载
  const simulateProgressiveLoading = useCallback((steps = [], totalDuration = 3000) => {
    if (!steps.length) return;

    const stepDuration = totalDuration / steps.length;
    let currentStep = 0;

    const executeStep = () => {
      if (currentStep < steps.length) {
        const step = steps[currentStep];
        updateProgress(step.progress, step.message);
        currentStep++;
        setTimeout(executeStep, stepDuration);
      } else {
        finishLoading();
      }
    };

    executeStep();
  }, [updateProgress, finishLoading]);

  return {
    isLoading,
    loadingMessage,
    progress,
    showProgress,
    startLoading,
    updateProgress,
    updateMessage,
    finishLoading,
    cancelLoading,
    simulateProgressiveLoading,
  };
};

export default useLoadingState;


