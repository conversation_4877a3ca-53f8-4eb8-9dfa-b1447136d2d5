import React from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Paper,
  Card,
  CardContent,
} from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  LockOutlined,
  ShoppingCartOutlined,
  ArrowForward,
  A<PERSON>,
  CreditCard,
} from "@mui/icons-material";
import { getAppNameFromPath } from "@/utils/subscriptionAccessControl";

/**
 * 订阅要求页面
 * 当用户尝试访问未订阅的微前端应用时显示
 */
const SubscriptionRequired = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const appName = getAppNameFromPath(location.pathname) || "微前端应用";

  const handleGoToSubscription = () => {
    navigate("/scriptionRoute");
  };

  const handleGoToAppCenter = () => {
    navigate("/application/center");
  };

  const handleGoHome = () => {
    navigate("/dashboard");
  };

  return (
    <Container maxWidth="md" sx={{ mt: 6, mb: 4 }}>
      <Paper
        elevation={4}
        sx={{
          p: 6,
          textAlign: "center",
          borderRadius: 4,
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
          position: "relative",
          overflow: "hidden",
        }}>
        {/* 背景装饰 */}
        <Box
          sx={{
            position: "absolute",
            top: -50,
            right: -50,
            width: 200,
            height: 200,
            borderRadius: "50%",
            background: "rgba(255, 255, 255, 0.1)",
            zIndex: 0,
          }}
        />
        <Box
          sx={{
            position: "absolute",
            bottom: -30,
            left: -30,
            width: 150,
            height: 150,
            borderRadius: "50%",
            background: "rgba(255, 255, 255, 0.05)",
            zIndex: 0,
          }}
        />

        {/* 内容区域 */}
        <Box sx={{ position: "relative", zIndex: 1 }}>
          {/* 图标区域 */}
          <Box sx={{ mb: 4 }}>
            <LockOutlined
              sx={{
                fontSize: 80,
                mb: 2,
                filter: "drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))",
              }}
            />
          </Box>

          {/* 标题 */}
          <Typography
            variant="h3"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: "bold",
              mb: 2,
              textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
            }}>
            需要订阅
          </Typography>

          {/* 副标题 */}
          <Typography
            variant="h5"
            gutterBottom
            sx={{
              mb: 4,
              opacity: 0.9,
            }}>
            您尚未订阅 {appName}
          </Typography>

          {/* 说明文字 */}
          <Box sx={{ mb: 5 }}>
            <Typography
              variant="body1"
              paragraph
              sx={{ fontSize: "1.2rem", lineHeight: 1.8, opacity: 0.9 }}>
              要访问 {appName} 应用，您需要先订阅相应的服务套餐。
            </Typography>
            <Typography
              variant="body1"
              paragraph
              sx={{ fontSize: "1.2rem", lineHeight: 1.8, opacity: 0.9 }}>
              请选择适合您需求的订阅方案，享受完整的应用功能。
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* 功能卡片区域 */}
      <Box
        sx={{
          mt: 4,
          display: "flex",
          gap: 3,
          flexWrap: "wrap",
          justifyContent: "center",
        }}>
        {/* 订阅卡片 */}
        <Card
          sx={{
            flex: "1 1 300px",
            maxWidth: 350,
            cursor: "pointer",
            transition: "all 0.3s ease",
            "&:hover": {
              transform: "translateY(-8px)",
              boxShadow: "0 12px 24px rgba(0, 0, 0, 0.15)",
            },
          }}
          onClick={handleGoToSubscription}>
          <CardContent sx={{ p: 4, textAlign: "center" }}>
            <ShoppingCartOutlined
              sx={{
                fontSize: 48,
                color: "#667eea",
                mb: 2,
              }}
            />
            <Typography
              variant="h6"
              gutterBottom
              sx={{ fontWeight: "bold", color: "#2c3e50" }}>
              立即订阅
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 3, lineHeight: 1.6 }}>
              浏览并选择适合您的订阅套餐，开始使用 {appName} 的强大功能
            </Typography>
            <Button
              variant="contained"
              endIcon={<ArrowForward />}
              sx={{
                background: "linear-gradient(45deg, #667eea 30%, #764ba2 90%)",
                "&:hover": {
                  background:
                    "linear-gradient(45deg, #667eea 60%, #764ba2 100%)",
                },
              }}>
              查看订阅方案
            </Button>
          </CardContent>
        </Card>

        {/* 应用中心卡片 */}
        <Card
          sx={{
            flex: "1 1 300px",
            maxWidth: 350,
            cursor: "pointer",
            transition: "all 0.3s ease",
            "&:hover": {
              transform: "translateY(-8px)",
              boxShadow: "0 12px 24px rgba(0, 0, 0, 0.15)",
            },
          }}
          onClick={handleGoToAppCenter}>
          <CardContent sx={{ p: 4, textAlign: "center" }}>
            <Apps
              sx={{
                fontSize: 48,
                color: "#78BC27",
                mb: 2,
              }}
            />
            <Typography
              variant="h6"
              gutterBottom
              sx={{ fontWeight: "bold", color: "#2c3e50" }}>
              应用中心
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 3, lineHeight: 1.6 }}>
              返回应用中心，查看您已订阅的应用或了解更多应用信息
            </Typography>
            <Button
              variant="outlined"
              endIcon={<ArrowForward />}
              sx={{
                borderColor: "#78BC27",
                color: "#78BC27",
                "&:hover": {
                  borderColor: "#78BC27",
                  backgroundColor: "rgba(120, 188, 39, 0.04)",
                },
              }}>
              前往应用中心
            </Button>
          </CardContent>
        </Card>
      </Box>

      {/* 底部操作区域 */}
      <Box sx={{ mt: 5, textAlign: "center" }}>
        <Button
          variant="text"
          onClick={handleGoHome}
          sx={{
            color: "#666",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.04)",
            },
          }}>
          返回首页
        </Button>
      </Box>

      {/* 帮助信息 */}
      <Box
        sx={{
          mt: 4,
          p: 3,
          backgroundColor: "rgba(103, 126, 234, 0.1)",
          borderRadius: 2,
          border: "1px solid rgba(103, 126, 234, 0.2)",
        }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mb: 2,
          }}>
          <CreditCard sx={{ mr: 1, color: "#667eea" }} />
          <Typography
            variant="h6"
            sx={{ color: "#667eea", fontWeight: "bold" }}>
            订阅说明
          </Typography>
        </Box>
        <Typography
          variant="body2"
          sx={{ color: "#4a5568", textAlign: "center", lineHeight: 1.6 }}>
          订阅后您将获得完整的应用功能访问权限，包括数据管理、高级功能和技术支持。
          如有疑问，请联系系统管理员或客服团队。
        </Typography>
      </Box>
    </Container>
  );
};

export default SubscriptionRequired;
