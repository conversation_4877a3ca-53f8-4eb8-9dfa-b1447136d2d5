import React from "react";
import { Box, Typography, LinearProgress, Chip, Stack } from "@mui/material";
import { Check, Close } from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import RegexCode from "@/enums/RegexCode";

const StyledLinearProgress = styled(LinearProgress)(({ theme, strength }) => ({
  height: 6,
  borderRadius: 3,
  backgroundColor: theme.palette.grey[200],
  "& .MuiLinearProgress-bar": {
    borderRadius: 3,
    backgroundColor:
      strength === "weak"
        ? theme.palette.error.main
        : strength === "medium"
        ? theme.palette.warning.main
        : strength === "strong"
        ? theme.palette.success.main
        : theme.palette.grey[300],
  },
}));

const RequirementChip = styled(Chip)(({ theme, met }) => ({
  fontSize: "0.75rem",
  height: 24,
  backgroundColor: met ? theme.palette.success.light : theme.palette.grey[100],
  color: met
    ? theme.palette.success.contrastText
    : theme.palette.text.secondary,
  "& .MuiChip-icon": {
    fontSize: "0.875rem",
  },
}));

// 密码强度计算函数
const calculatePasswordStrength = (password) => {
  if (!password) return { score: 0, strength: "none" };

  let score = 0;
  const requirements = {
    length: password.length >= 12, // 更新为12位最小长度
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[^\w\s]/.test(password), // 使用与正则表达式一致的特殊字符检测
  };

  // 基础分数
  if (requirements.length) score += 20;
  if (requirements.lowercase) score += 15;
  if (requirements.uppercase) score += 15;
  if (requirements.number) score += 15;
  if (requirements.special) score += 15;

  // 长度奖励
  if (password.length >= 12) score += 10;
  if (password.length >= 16) score += 10;

  // 确定强度等级
  let strength = "weak";
  if (score >= 80) strength = "strong";
  else if (score >= 60) strength = "medium";

  return { score, strength, requirements };
};

const PasswordStrengthIndicator = ({
  password = "",
  showRequirements = true,
  showScore = false,
}) => {
  const { t } = useTranslation();
  const { score, strength, requirements } = calculatePasswordStrength(password);

  const strengthLabels = {
    none: t("password_strength.none"),
    weak: t("password_strength.weak"),
    medium: t("password_strength.medium"),
    strong: t("password_strength.strong"),
  };

  const strengthColors = {
    none: "default",
    weak: "error",
    medium: "warning",
    strong: "success",
  };

  const requirementsList = [
    {
      key: "length",
      label: t("password_strength.requirement_length"),
      met: requirements?.length,
    },
    {
      key: "lowercase",
      label: t("password_strength.requirement_lowercase"),
      met: requirements?.lowercase,
    },
    {
      key: "uppercase",
      label: t("password_strength.requirement_uppercase"),
      met: requirements?.uppercase,
    },
    {
      key: "number",
      label: t("password_strength.requirement_number"),
      met: requirements?.number,
    },
    {
      key: "special",
      label: t("password_strength.requirement_special"),
      met: requirements?.special,
    },
  ];

  if (!password && !showRequirements) return null;

  return (
    <Box sx={{ mt: 1 }}>
      {/* 密码强度指示器 */}
      <Box sx={{ mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 1,
          }}>
          <Typography variant="body2" color="text.secondary">
            {t("password_strength.title")}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            {showScore && (
              <Typography variant="body2" color="text.secondary">
                {score}/100
              </Typography>
            )}
            {/* <Chip
              label={strengthLabels[strength]}
              size="small"
              color={strengthColors[strength]}
              variant={strength === "none" ? "outlined" : "filled"}
            /> */}
          </Box>
        </Box>

        <StyledLinearProgress
          variant="determinate"
          value={score}
          strength={strength}
        />
      </Box>

      {/* 密码要求列表 */}
      {showRequirements && password && (
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {t("password_strength.requirements_title")}
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {requirementsList.map((req) => (
              <RequirementChip
                key={req.key}
                label={req.label}
                size="small"
                met={req.met}
                icon={req.met ? <Check /> : <Close />}
              />
            ))}
          </Stack>
        </Box>
      )}
    </Box>
  );
};

// 密码验证工具函数
export const validatePassword = (password, currentPassword = "", t = null) => {
  const errors = [];

  if (!password) {
    errors.push(t("password_strength.error_required"));
    return errors;
  }

  // 使用统一的正则表达式进行验证
  if (!RegexCode.PASSWORD_REGEX.test(password)) {
    if (password.length < 12) {
      errors.push(t("password_strength.error_min_length"));
    } else if (password.length > 64) {
      errors.push(t("password_strength.error_max_length"));
    } else if (!/[a-z]/.test(password)) {
      errors.push(t("password_strength.error_lowercase"));
    } else if (!/[A-Z]/.test(password)) {
      errors.push(t("password_strength.error_uppercase"));
    } else if (!/\d/.test(password)) {
      errors.push(t("password_strength.error_number"));
    } else if (!/[^\w\s]/.test(password)) {
      errors.push(t("password_strength.error_special"));
    } else if (
      /[\s\u{1F300}-\u{1F9FF}\u{2600}-\u{27BF}\u{1F1E6}-\u{1F1FF}]/u.test(
        password
      )
    ) {
      errors.push(t("password_strength.error_no_spaces_emoji"));
    } else {
      errors.push(t("common.common_confirm_password_format"));
    }
  }

  if (currentPassword && password === currentPassword) {
    errors.push(t("password_strength.error_same_as_current"));
  }

  return errors;
};

// 检查密码强度是否足够
export const isPasswordStrong = (password) => {
  const { strength } = calculatePasswordStrength(password);
  return strength === "strong" || strength === "medium";
};

export default PasswordStrengthIndicator;
