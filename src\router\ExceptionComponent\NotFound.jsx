import React from "react";
import {
  Box,
  Typography,
  Button,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";
import { Home, ArrowBack, Search, Refresh } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

// 苹果风格的动画
const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// 主容器 - 苹果风格的渐变背景
const MainContainer = styled(Box)(({ theme }) => ({
  minHeight: "100vh",
  background: `linear-gradient(135deg, 
    ${theme.palette.mode === "dark" ? "#1a1a1a" : "#f8f9fa"} 0%, 
    ${theme.palette.mode === "dark" ? "#2d2d2d" : "#e9ecef"} 100%)`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(2),
  position: "relative",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 30% 20%, 
      ${theme.palette.primary.main}15 0%, 
      transparent 50%),
      radial-gradient(circle at 70% 80%, 
      ${theme.palette.secondary.main}10 0%, 
      transparent 50%)`,
    pointerEvents: "none",
  },
}));

// 内容卡片 - 苹果风格的毛玻璃效果
const ContentCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6, 4),
  borderRadius: theme.spacing(3),
  textAlign: "center",
  maxWidth: 600,
  width: "100%",
  background:
    theme.palette.mode === "dark"
      ? "rgba(255, 255, 255, 0.05)"
      : "rgba(255, 255, 255, 0.9)",
  backdropFilter: "blur(20px)",
  border: `1px solid ${
    theme.palette.mode === "dark"
      ? "rgba(255, 255, 255, 0.1)"
      : "rgba(255, 255, 255, 0.2)"
  }`,
  boxShadow:
    theme.palette.mode === "dark"
      ? "0 8px 32px rgba(0, 0, 0, 0.3)"
      : "0 8px 32px rgba(0, 0, 0, 0.1)",
  animation: `${fadeIn} 0.8s ease-out`,
  position: "relative",
  zIndex: 1,
}));

// 404数字 - 大号显示
const ErrorNumber = styled(Typography)(({ theme }) => ({
  fontSize: "clamp(6rem, 15vw, 12rem)",
  fontWeight: 700,
  background: `linear-gradient(135deg, 
    ${theme.palette.primary.main}, 
    ${theme.palette.secondary.main})`,
  backgroundClip: "text",
  WebkitBackgroundClip: "text",
  WebkitTextFillColor: "transparent",
  lineHeight: 0.8,
  marginBottom: theme.spacing(2),
  animation: `${float} 3s ease-in-out infinite`,
  textShadow:
    theme.palette.mode === "dark"
      ? "0 0 30px rgba(255, 255, 255, 0.1)"
      : "0 0 30px rgba(0, 0, 0, 0.1)",
}));

// 苹果风格的按钮
const AppleButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: theme.spacing(3),
  padding: theme.spacing(1.5, 4),
  textTransform: "none",
  fontWeight: 600,
  fontSize: "1rem",
  minWidth: 140,
  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  ...(variant === "primary" && {
    background: `linear-gradient(135deg, 
      ${theme.palette.primary.main}, 
      ${theme.palette.primary.dark})`,
    color: theme.palette.primary.contrastText,
    boxShadow: `0 4px 20px ${theme.palette.primary.main}40`,
    "&:hover": {
      transform: "translateY(-2px)",
      boxShadow: `0 8px 25px ${theme.palette.primary.main}50`,
    },
  }),
  ...(variant === "secondary" && {
    backgroundColor:
      theme.palette.mode === "dark"
        ? "rgba(255, 255, 255, 0.1)"
        : "rgba(0, 0, 0, 0.05)",
    color: theme.palette.text.primary,
    border: `1px solid ${
      theme.palette.mode === "dark"
        ? "rgba(255, 255, 255, 0.2)"
        : "rgba(0, 0, 0, 0.1)"
    }`,
    "&:hover": {
      backgroundColor:
        theme.palette.mode === "dark"
          ? "rgba(255, 255, 255, 0.15)"
          : "rgba(0, 0, 0, 0.08)",
      transform: "translateY(-1px)",
    },
  }),
}));

// 装饰性图标
const FloatingIcon = styled(Box)(({ theme }) => ({
  position: "absolute",
  color: theme.palette.primary.main,
  opacity: 0.1,
  fontSize: "8rem",
  animation: `${float} 4s ease-in-out infinite`,
  zIndex: 0,
}));

const NotFound = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleGoHome = () => {
    navigate("/");
  };

  const handleGoBack = () => {
    // 检查是否有历史记录可以返回
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      // 如果没有历史记录，则导航到首页
      navigate("/");
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // 确保所有hooks都在函数的顶层被调用，没有任何条件判断

  return (
    <MainContainer>
      {/* 装饰性浮动图标 */}
      <FloatingIcon sx={{ top: "10%", left: "10%", animationDelay: "0s" }}>
        <Search />
      </FloatingIcon>
      <FloatingIcon sx={{ top: "20%", right: "15%", animationDelay: "1s" }}>
        <Home />
      </FloatingIcon>
      <FloatingIcon sx={{ bottom: "15%", left: "20%", animationDelay: "2s" }}>
        <Refresh />
      </FloatingIcon>

      <Container
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}>
        <ContentCard elevation={0}>
          {/* 404 数字 */}
          <ErrorNumber variant="h1">404</ErrorNumber>

          {/* 主标题 */}
          <Typography
            variant={isMobile ? "h4" : "h3"}
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: "text.primary",
              mb: 2,
            }}>
            页面未找到
          </Typography>

          {/* 副标题 */}
          <Typography
            variant="h6"
            color="text.secondary"
            paragraph
            sx={{
              mb: 4,
              lineHeight: 1.6,
              maxWidth: 400,
              mx: "auto",
            }}>
            抱歉，您访问的页面不存在或已被移动。
            请检查网址是否正确，或返回首页继续浏览。
          </Typography>

          {/* 操作按钮 */}
          <Box
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
              flexWrap: "wrap",
              mt: 4,
            }}>
            <AppleButton
              variant="primary"
              startIcon={<Home />}
              onClick={handleGoHome}>
              返回首页
            </AppleButton>

            <AppleButton
              variant="secondary"
              startIcon={<ArrowBack />}
              onClick={handleGoBack}>
              返回上页
            </AppleButton>
          </Box>

          {/* 额外操作 */}
          <Box
            sx={{
              display: "flex",
              gap: 1,
              justifyContent: "center",
              mt: 3,
            }}>
            <Button
              size="small"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              sx={{
                color: "text.secondary",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "action.hover",
                },
              }}>
              刷新页面
            </Button>
          </Box>

          {/* 帮助信息 */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mt: 4,
              opacity: 0.7,
              fontSize: "0.875rem",
            }}>
            如果问题持续存在，请联系技术支持
          </Typography>
        </ContentCard>
      </Container>
    </MainContainer>
  );
};

export default NotFound;
