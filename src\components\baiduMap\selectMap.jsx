/* eslint-disable no-undef */
import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useConfirm } from "@/components/zkconfirm";
import { NavigationControl, Map } from "react-bmapgl";
import CitySelect from "./CitySelect";
import MapTextAutoComplete from "./mapTextAutoComplete";
import {
  BootstrapDialog,
  BootstrapContent,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";

const selectMap = forwardRef((props, ref) => {
  const { t } = useTranslation();
  // 传递事件给父组件调用
  useImperativeHandle(ref, () => ({
    handleClose,
    handleClickOpen,
  }));
  const searchRef = useRef(null);
  const [open, setOpen] = React.useState(false);
  const confirm = useConfirm();
  const mapRef = React.useRef(null);
  const [pointList, setPointList] = React.useState([]);
  const [selectAddress, setSelectAddress] = useState("");
  const [code, setCode] = useState(undefined);
  const [inputAddress, setInputAddress] = useState("");
  const [zoom, setZoom] = useState(10);
  const [center, setCenter] = useState({});

  const getNowLocation = () => {
    return new Promise((resolve, reject) => {
      const geolocation = new BMapGL.Geolocation();
      geolocation.getCurrentPosition(function (r) {
        if (this.getStatus() === BMAP_STATUS_SUCCESS) {
          // var mk = new BMapGL.Marker(r.point);
          // return r.point;
          setCode(r.address.city);
          resolve(r.point);
        } else {
          toast.error(t("common.common_current_location_error"));
          reject(t("common.common_current_location_error"));
        }
      });
    });
  };
  // 搜索
  const handleSearch = (detailsAddress, code, address) => {
    const geocoder = new BMapGL.Geocoder();
    geocoder.getPoint(detailsAddress, (point) => {
      if (point) {
        setCenter({
          lng: point.lng,
          lat: point.lat,
        });
        let zoom = 8;
        console.log(address.indexOf("区"));
        if (address.indexOf("市") > 0) {
          zoom = 10;
        } else if (address.indexOf("区") > 0 || address.indexOf("县") > 0) {
          zoom = 14;
        }
        mapRef.current.map.centerAndZoom(detailsAddress, zoom);
        // 传值到city
        console.log(address);
        if (!(address.indexOf("区") > 0 || address.indexOf("县") > 0)) {
          setCode(address);
        }
      }
    });
  };

  const handleChangeSelectAddress = (address) => {
    setSelectAddress(address);
  };
  const handleTextOnChange = () => {
    setInputAddress(searchRef.current.value);
  };

  // 根据坐标得到地址描述
  const Geolocation = async (lng, lat) => {
    return new Promise((resolve, reject) => {
      const myGeo = new BMapGL.Geocoder();
      // 根据坐标得到地址描述
      myGeo.getLocation(new BMapGL.Point(lng, lat), function (result) {
        if (result) {
          resolve(result);
        } else {
          reject(null);
        }
      });
    });
  };

  const handleMapClick = () => {
    mapRef.current.map.addEventListener("click", async function (e) {
      const location = await Geolocation(e.latlng.lng, e.latlng.lat);
      const lng = e.latlng.lng;
      const lat = e.latlng.lat;
      confirm({
        title: t("common.common_confirm_location"),
        confirmationText: t("common.common_edit_ok"),
        cancellationText: t("common.common_edit_cancel"),
        description: t("common.common_select_current_location", {
          address: location.address,
          lng: String(lng),
          lat: String(lat),
        }),
      })
        .then(() => {
          props.getLocation(e.point, location.address);
          handleClose();
        })
        .catch(() => {});
    });
    const currentPosition = props.currentPosition;
    if (currentPosition && props.storeInfo) {
      setCenter(currentPosition);
      setZoom(18);
      mapRef.current.map.addOverlay(
        new BMapGL.Marker(props.currentPosition, { title: "aaa" })
      );
      let infoWindow = new BMapGL.InfoWindow(
        `<span style="padding-top:10px">${t(
          "common.common_current_location_e"
        )}: ${props.storeInfo.address}  <br/>${t(
          "common.common_current_lng_lat"
        )}：${currentPosition.lng} <br/> ${currentPosition.lat}</span>`,
        {
          width: 250, // 信息窗口宽度
          height: 120, // 信息窗口高度
          title:
            props.storeInfo.name + `-${t("common.common_store_location_info")}`, // 信息窗口标题
        }
      ); // 创建信息窗口对象
      mapRef.current.map.openInfoWindow(
        infoWindow,
        new BMapGL.Point(currentPosition.lng, currentPosition.lat)
      ); //开启信息窗口
      const location = Geolocation(currentPosition.lng, currentPosition.lat);
      if (location && location.addressComponents) {
        setCode(location.addressComponents.city);
      }
      // setCode()
    } else {
      // 新建打开，获取当前浏览器的ip地址拿到经纬度
      getNowLocation().then((point) => {
        setCenter(point);
      });
    }
  };

  // 页面打开事件
  const handleClickOpen = () => {
    setOpen(true);
    // 地图点击事件拿到经纬度
    // setTimeout(async () => {
    //   mapRef.current.map.addEventListener("click", async function (e) {
    //     const location = await Geolocation(e.latlng.lng, e.latlng.lat);
    //     const lng = e.latlng.lng;
    //     const lat = e.latlng.lat;
    //     confirm({
    //       title: t("common.common_confirm_location"),
    //       description: t("common.common_select_current_location", {
    //         address: location.address,
    //         lng: String(lng),
    //         lat: String(lat),
    //       }),
    //       //  `当前选择的城市为：${location.address}，选择的经纬度为：${e.latlng.lng},${e.latlng.lat}`,
    //     })
    //       .then(() => {
    //         props.getLocation(e.latlng, location);
    //         handleClose();
    //       })
    //       .catch(() => {});
    //   });
    //   const currentPosition = props.currentPosition;
    //   if (currentPosition && props.storeInfo) {
    //     setCenter(currentPosition);
    //     setZoom(18);
    //     mapRef.current.map.addOverlay(
    //       new BMapGL.Marker(props.currentPosition, { title: "aaa" })
    //     );
    //     let infoWindow = new BMapGL.InfoWindow(
    //       `<span style="padding-top:10px">${t(
    //         "common.common_current_location_e"
    //       )}: ${props.storeInfo.address}  <br/>${t(
    //         "common.common_current_lng_lat"
    //       )}：${currentPosition.lng} <br/> ${currentPosition.lat}</span>`,
    //       {
    //         width: 250, // 信息窗口宽度
    //         height: 120, // 信息窗口高度
    //         title:
    //           props.storeInfo.name +
    //           `-${t("common.common_store_location_info")}`, // 信息窗口标题
    //       }
    //     ); // 创建信息窗口对象
    //     mapRef.current.map.openInfoWindow(
    //       infoWindow,
    //       new BMapGL.Point(currentPosition.lng, currentPosition.lat)
    //     ); //开启信息窗口
    //     const location = await Geolocation(
    //       currentPosition.lng,
    //       currentPosition.lat
    //     );
    //     if (location && location.addressComponents) {
    //       setCode(location.addressComponents.city);
    //     }
    //     // setCode()
    //   } else {
    //     // 新建打开，获取当前浏览器的ip地址拿到经纬度
    //     getNowLocation().then((point) => {
    //       setCenter(point);
    //     });
    //   }
    // });
  };
  // 页面关闭事件
  const handleClose = () => {
    mapRef.current = null;
    setOpen(false);
  };

  useEffect(() => {
    if (open) {
      setTimeout(() => {
        handleMapClick();
      }, 500);
    }
  }, [open]);

  const handleCenter = (value) => {
    debugger;
    if (value) {
      mapRef.current.map.centerAndZoom(value.location, 18);
      // console.log(mapRef.current.map);
      mapRef.current.map.clearOverlays();
      mapRef.current.map.addOverlay(
        new BMapGL.Marker(value.location, { title: value.name })
      );
      let infoWindow = new BMapGL.InfoWindow(
        `<span style="padding-top:10px">${t("common.common_search_info")}: ${
          value.name
        } <br /><span style="word-wrap:break-word;
                word-break:break-all;">${t("common.common_location_e")}: ${
          value.address
        }</span></span>`,
        {
          width: 200, // 信息窗口宽度
          height: 120, // 信息窗口高度
          title: t("common.common_search_info"), // 信息窗口标题
        }
      ); // 创建信息窗口对象
      mapRef.current.map.openInfoWindow(
        infoWindow,
        new BMapGL.Point(value.location.lng, value.location.lat)
      ); //开启信息窗口
    }
  };

  return (
    <>
      <BootstrapDialog
        fullWidth
        maxWidth="lg"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}>
        <BootstrapDialogTitle
          id="customized-dialog-title"
          onClose={handleClose}>
          <Typography variant="h4" component="p">
            {t("common.common_select_location")}
          </Typography>
        </BootstrapDialogTitle>
        <BootstrapContent>
          <div className="flex flex-col gap-2 w-full">
            <div className="flex items-center justify-end">
              <CitySelect
                search={handleSearch}
                changeSelectAddress={handleChangeSelectAddress}
              />
              <MapTextAutoComplete
                setCenter={handleCenter}
                code={code}
                placeholder={t("common.common_input_location_search")}
              />
            </div>
            <div className="flex-1">
              <Map
                ref={mapRef}
                style={{ height: 500 }}
                enableScrollWheelZoom
                center={new BMapGL.Point(center.lng, center.lat)}
                zoom={zoom}
                tilt={40}>
                <NavigationControl />
              </Map>
            </div>
          </div>
        </BootstrapContent>
      </BootstrapDialog>
    </>
  );
});

export default selectMap;
