/* eslint-disable no-unused-vars */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */
import { getToken } from "@/utils/auth";
import { getLoginInfor } from "@/service/api/user";
import { getMenuList } from "@s/api/premission";
import { store } from "@/store";
import { setMenuList } from "@/store/reducers/menu";
import i18n from "i18next";
import { setInfoLoaded, setUserInfor, setPermission } from "@/store/reducers/user";
import { toast } from 'react-toastify';
import { ensureAppListLoaded } from "@/utils/appListManager";
import {
  validateMicroAppAccess,
  getAppNameFromPath
} from "@/utils/subscriptionAccessControl";

// 路由常量
const ROUTES = {
  LOGIN: '/login',
  ERROR_PAGES: {
    NOT_FOUND: '/404',
    FORBIDDEN: '/403',
    SERVER_ERROR: '/500',
    NETWORK_ERROR: '/network-error',
    SUBSCRIPTION_REQUIRED: '/subscription-required'
  }
};

// 微前端应用路径前缀
const MICRO_APP_PREFIXES = [
  '/cms-app',
  '/retail-ai-app',
  '/e-price-tag-app'
];

// 应用配置
const APP_CONFIG = {
  APPLICATION_CODE: 'L3'
};

const flattenTree = (nodes) => {
  let result = [];
  nodes.forEach((node) => {
    result.push(node.id);
    if (node.children) {
      let ids = flattenTree(node.children);
      result = [...result, ...ids];
    }
  });
  return result;
};


// 权限路由映射缓存
let menuPathsMap = new Map();
let lastMenuHash = ''; // 用于检测菜单是否变化

// 计算菜单列表的哈希值，用于检测变化
const calculateMenuHash = (menuList) => {
  return JSON.stringify(menuList)
    .split('')
    .reduce((a, b) => (((a << 5) - a) + b.charCodeAt(0)) | 0, 0)
    .toString();
};

// 构建权限路径映射
function buildMenuPathsMap(menuList) {
  // 清空旧的映射
  menuPathsMap.clear();

  const processMenu = (menu) => {
    if (menu.path) {
      const normalizedPath = menu.path.startsWith('/') ? menu.path : '/' + menu.path;
      // 存储完整的菜单项信息，而不是简单的 true
      menuPathsMap.set(normalizedPath, {
        path: normalizedPath,
        meta: menu.meta,
        requiresAuth: true
      });
    }
    if (menu.children) {
      menu.children.forEach(processMenu);
    }
  };

  menuList.forEach(processMenu);

  // 更新菜单哈希值
  lastMenuHash = calculateMenuHash(menuList);
}

// 检查菜单中是否包含指定路径的菜单项
const hasMenuPermission = (menuList, path) => {
  if (!menuList || !Array.isArray(menuList)) {
    return false;
  }

  // 检查菜单是否发生变化
  const currentHash = calculateMenuHash(menuList);
  if (currentHash !== lastMenuHash) {
    buildMenuPathsMap(menuList);
  }

  const normalizedPath = path.startsWith('/') ? path : '/' + path;

  // 检查是否是免权限路由
  if (ROUTES.ERROR_PAGES[normalizedPath] || normalizedPath === ROUTES.LOGIN) {
    return true;
  }

  // 检查是否有权限访问
  const routeConfig = menuPathsMap.get(normalizedPath);
  return !!routeConfig;
};

// 检查是否是错误页面
const isErrorPage = (pathname) => {
  return Object.values(ROUTES.ERROR_PAGES).includes(pathname);
};

// 检查是否是微前端路径
const isMicroAppPath = (pathname) => {
  return MICRO_APP_PREFIXES.some(prefix => pathname.startsWith(prefix));
};

// 处理微前端路由访问
const handleMicroAppRouting = async (pathname) => {
  let appList = [];
  try {
    appList = await ensureAppListLoaded();
  } catch (error) {
    toast.error('获取应用列表失败');
    return ROUTES.ERROR_PAGES.NETWORK_ERROR;
  }

  const accessResult = validateMicroAppAccess(pathname, appList);
  if (!accessResult.allowed) {
    const appName = getAppNameFromPath(pathname);
    if (accessResult.reason === 'not_subscribed') {
      return ROUTES.ERROR_PAGES.SUBSCRIPTION_REQUIRED;
    }
    toast.error(`无法访问 ${appName}`);
    return ROUTES.ERROR_PAGES.NOT_FOUND;
  }
  return null;
};

// 加载用户信息和菜单
const loadUserInfoAndMenu = async (pathname, meta) => {
  // 如果是登录页，不需要检查权限
  if (pathname === ROUTES.LOGIN) {
    return null;
  }

  try {

    const [userRes, menuRes] = await Promise.all([
      getLoginInfor(),
      getMenuList({ applicationCode: APP_CONFIG.APPLICATION_CODE }),
    ]);

    if (!userRes?.data || !menuRes?.data) {
      return ROUTES.LOGIN;
    }


    // 更新状态
    store.dispatch(setUserInfor(userRes.data));
    store.dispatch(setPermission(userRes.data.permissions));


    store.dispatch(setMenuList(menuRes.data));
    store.dispatch(setInfoLoaded(true));

    // 检查路由权限
    if (meta?.id && !hasMenuPermission(menuRes.data, pathname)) {
      console.log(`用户没有权限访问路径 ${pathname}，跳转到403页面`);
      return ROUTES.ERROR_PAGES.FORBIDDEN;
    }

    return null;
  } catch (err) {


    return ROUTES.LOGIN;
  }
};

const onRouteBefore = async ({ pathname, meta }) => {
  // 1. 如果是登录页，直接放行
  if (pathname === ROUTES.LOGIN) {
    return null;
  }

  // 2. 如果是错误页面，直接放行
  if (isErrorPage(pathname)) {
    return null;
  }

  // 3. 设置页面标题
  if (meta?.i18n) {
    document.title = i18n.t("menu." + meta.i18n);
  }

  // 4. 处理微前端路由
  if (isMicroAppPath(pathname)) {
    return await handleMicroAppRouting(pathname);
  }

  // 5. 处理需要权限的路由
  try {
    if (meta.id || meta.idEditor) {
      // 编辑器模式不需要token
      if (!meta.idEditor && !getToken()) {
        return ROUTES.LOGIN;
      }

      const state = store.getState();
      if (!state.user.userInfor || !state.user.isInfoLoaded) {
        return await loadUserInfoAndMenu(pathname, meta);
      }

      // 已登录用户访问登录页时重定向到首页
      if (pathname === ROUTES.LOGIN && state.user.userInfor) {
        return '/';
      }
    }
    return null;
  } catch (error) {
    toast.error("路由守卫错误，请重新登录");
    return ROUTES.LOGIN;
  }
};

export default onRouteBefore;