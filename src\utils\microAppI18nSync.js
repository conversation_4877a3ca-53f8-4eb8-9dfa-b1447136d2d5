/**
 * 微前端国际化同步工具
 * 用于在主应用和子应用之间同步语言设置
 */

import { updateLanguage } from './actions';
import { getStoreLang, setStoreLang } from './langUtils';

/**
 * 初始化微前端国际化同步
 * 在主应用启动时调用
 */
export const initMicroAppI18nSync = () => {
  console.log('🌐 初始化微前端国际化同步');

  // 监听语言变化事件
  window.addEventListener('languageChange', (event) => {
    const { language } = event.detail;
    console.log(`🌐 收到语言变化事件: ${language}`);

    // 更新本地存储
    setStoreLang(language);

    // 更新全局状态，通知所有子应用
    updateLanguage(language);

    // 更新主应用的国际化
    if (window.i18n) {
      window.i18n.changeLanguage(language);
    }
  });

  // 监听主应用语言变化事件（不刷新页面的情况）
  window.addEventListener('main-app-language-changed', (event) => {
    const { language } = event.detail;
    console.log(`🌐 主应用语言变化: ${language}`);

    // 强制更新所有React组件
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('force-react-update'));
    }, 100);
  });

  // 初始化时同步当前语言到全局状态
  const currentLang = getStoreLang();
  updateLanguage(currentLang);
  console.log(`✅ 微前端国际化同步初始化完成，当前语言: ${currentLang}`);
};

/**
 * 触发语言变化事件
 * 在语言切换时调用
 */
export const triggerLanguageChange = (language) => {
  const event = new CustomEvent('languageChange', {
    detail: { language }
  });
  window.dispatchEvent(event);
};

/**
 * 为子应用提供的国际化配置
 * 子应用可以通过 props 获取这些配置
 */
export const getMicroAppI18nConfig = () => {
  return {
    language: getStoreLang(),
    i18nInstance: window.i18n,
    // 提供语言切换方法给子应用
    changeLanguage: (language) => {
      triggerLanguageChange(language);
    }
  };
};

/**
 * 子应用国际化初始化辅助函数
 * 子应用可以调用此函数来同步主应用的语言设置
 */
export const initSubAppI18n = (subAppI18n, props) => {
  if (!props || !props.actions) {
    console.warn('子应用未接收到主应用的 actions，无法同步国际化');
    return;
  }

  // 从主应用获取当前语言
  const mainAppLanguage = props.language || getStoreLang();

  // 设置子应用的语言
  if (subAppI18n && subAppI18n.changeLanguage) {
    subAppI18n.changeLanguage(mainAppLanguage);
  }

  // 监听主应用的语言变化
  props.actions.onGlobalStateChange((state) => {
    if (state.language && state.language !== subAppI18n.language) {
      // 同步语言变化
      if (subAppI18n && subAppI18n.changeLanguage) {
        subAppI18n.changeLanguage(state.language);
      }

      // 更新本地存储
      setStoreLang(state.language);
    }
  });

  return {
    currentLanguage: mainAppLanguage,
    syncLanguage: (language) => {
      // 子应用主动同步语言到主应用
      props.actions.setGlobalState({
        language
      });
    }
  };
};

/**
 * 检查子应用是否支持国际化同步
 */
export const checkSubAppI18nSupport = (props) => {
  return !!(props && props.actions && props.language);
};
