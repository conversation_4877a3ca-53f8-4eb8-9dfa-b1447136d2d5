import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import { getParnerListUser } from "@s/api/partner";
import { useTranslation } from "react-i18next";
import { useTableRequest, getDepartmentId } from "../../utils.js"; // 引入

const index = () => {
  const { t } = useTranslation();
  const [serchName, setSeachName] = useState("");
  const departmentId = getDepartmentId();
  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getParnerListUser, { departmentId: departmentId });

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName, departmentId: departmentId });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset({ departmentId: departmentId });
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("partner_user.title")}
        isSearch={true}
        serchName={serchName}
        onClick={handlerSeacher}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加 onClear 属性
        content={rederTable()}></LayoutList>
    </React.Fragment>
  );
};

export default index;
