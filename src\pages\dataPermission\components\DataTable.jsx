import React from "react";
import SvgIcon from "@c/SvgIcon";
import { useTranslation } from "react-i18next";
import { tableI18n } from "@/utils/tableLang";
import { toast } from "react-toastify";
import MaterialReactTable from "material-react-table";
import PropTypes from "prop-types";
function DataTable(props) {
  const { t } = useTranslation();
  const {
    mrt = true,
    renderRowActions = ({ table }) => null,
    renderToolbarInternalActions = ({ table }) => null, //头部右侧
    renderTopToolbarCustomActions = () => null, //头部左侧
    renderBottomToolbarCustomActions = () => null, //底部左侧工具
    positionToolbarAlertBanner = "none", // 多选底部提示
    enablePagination = true,
    onPaginationChange = () => {}, // 分页回调函数
    minHeight = "70vh",
    maxHeight = "calc(100vh - 260px )",
    columns = [],
    state,
    data = [],
    filterFromLeafRows = true,
    enableColumnResizing = false,
    showTopBar = true,
    isLoading,
    pagination,
    isRefetching,
    isError,
    rowCount,
    manualPagination,
    manualSorting,
    enableExpanding = "false",
    onPageChange = (e) => {},
    onPageSizeChange = (e) => {},
    pathRoute,
    loadDada,
    totalRecords = "0",
    rowsPerPage = "5",
    currentPage = "1",
    Detele,
    viewRoute,
    enableRowActions = true,
    handlerUnion,
    showAdd = false,
    showRefresh = true,
    showDownload = false,
    showUpload = false,
    addCallback,
    actionWidth = 160,
    headerTitle,
    isShowTop = true,
    tableInstanceRef,
    selectNumber,
    refreshCallback = () => {
      props?.loadDada();
      toast.success(t("common.common_refresh_success"));
    },
    initialState,
    ...orther
  } = props;

  return (
    <React.Fragment>
      <Typography
        style={{
          marginBottom: "10px",
          fontWeight: 600,
          fontSize: "16px",
        }}>
        {props?.title}
        {selectNumber && (
          <span
            style={{ color: "#1487CA", fontSize: "14px", marginLeft: "10px" }}>
            - {rowCount} {t("datascope.selected")}
          </span>
        )}
      </Typography>

      <MaterialReactTable
        renderToolbarInternalActions={({ table }) => null}
        tableInstanceRef={tableInstanceRef} // 绑定 ref
        muiTablePaperProps={{
          elevation: 0,
          sx: {
            borderRadius: "5px",

            "& .MuiTableCell-body": {
              justifyContent: "center",
              alignItems: "center",
              textAlign: "center",
            },
          },
        }}
        muiTableHeadRowProps={{
          sx: {
            boxShadow: "none",
            height: "60px",

            "& .Mui-TableHeadCell-Content": {
              justifyContent: "center",
              alignItems: "center",
              lineHeight: "34px",
              fontSize: "14px",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
            },
          },
        }}
        muiTableHeadCellProps={{
          sx: {
            backgroundColor: "#ebecf0", // 表头的背景色
          },
        }}
        renderEmptyRowsFallback={({ table }) => {
          return <NoResultFound></NoResultFound>;
        }}
        mantineTableBodyRowProps={{
          muiTableBodyCellProps: {
            sx: {
              display: "flex",
              justifyContent: "center", // 水平居中
              alignItems: "center", // 垂直居中
              textAlign: "center", // 文字居中
              maxHeight: "60%",
              borderRadius: "8px",
              "& .MuiTableRow-root": {
                fontSize: "14px",
              },
            },
          },
        }}
        debugRows={false}
        // 条纹列
        mantinePaperProps={{
          shadow: "none",
          sx: {
            borderRadius: "0",
            border: "1px dashed #e0e0e0",
          },
        }}
        mantineTableProps={{
          striped: true,
        }}
        layoutMode="grid"
        //将筛选应用于所有子行

        pagination={pagination}
        //
        enableEditing={false}
        // table 状态
        state={{
          columnPinning: { right: ["mrt-row-actions"] },
          isLoading: isLoading,
          isRefetching: isRefetching,
          isError: isError,
          isLoading: isLoading,
          ...state,
        }}
        // 列 菜单项
        renderColumnActionsMenuItems={{}}
        //启用列绑定
        enablePinning={true}
        // 启用列排序
        enableColumnOrdering={false}
        // 启用列扩展
        enableExpandAll={false}
        // 解决列太多宽度太长问题
        enableColumnResizing
        enableFilterMatchHighlighting={false}
        //启用过滤器
        enableFilters={false}
        //启用全局过滤器
        enableGlobalFilter={false}
        // 启用全局全屏
        enableFullScreenToggle={false}
        // 启用多重排序
        enableMultiSort={false}
        // 启用行排序
        enableRowOrdering={false}
        // 启用复制
        enableClickToCopy={false}
        //启用排序
        enableSorting={false}
        //启用顶部工具栏
        enableTopToolbar={false}
        manualExpanding={false}
        manualSorting={false}
        // 启用底部工具栏
        enableBottomToolbar
        positionGlobalFilter={"right"}
        // 初始化状态
        initialState={{ expanded: true }}
        muiToolbarAlertBannerProps={
          isError
            ? {
                color: "error",
                children: t("table.loading_error"),
              }
            : undefined
        }
        // 列数
        rowCount={rowCount}
        // 固定头部
        enableStickyHeader={true}
        // 处理表格高度
        muiTableContainerProps={{ sx: { maxHeight: "100%" } }}
        // 设置背景颜色
        muiTableBodyCellProps={{
          sx: {
            maxHeight: "200px",
            "& .MuiTableCell-body": {
              justifyContent: "center",
              alignItems: "center",
              textAlign: "center",
              fontSize: "14px",
              overflowX: "auto",
            },
          },
        }}
        muiTableProps={{ sx: { backgroundColor: "white" } }}
        muiBottomToolbarProps={{ sx: { backgroundColor: "white" } }}
        muiTopToolbarProps={{ sx: { backgroundColor: "white" } }}
        mantineTableContainerProps={{
          sx: {
            maxHeight: "200px",
            "& .MuiTableCell-body": {
              justifyContent: "center",
              alignItems: "center",
              textAlign: "center",
              fontSize: "14px",
            },
          },
        }}
        // 分页回调函数
        onPaginationChange={onPaginationChange}
        // 记得开启这些 tips:否则会出现useEffect 反作用函数发起多次请求点击下一页会出问题
        manualFiltering
        // 开启分页
        enablePagination={false}
        //启用列操作
        enableColumnActions={false}
        // 列定义
        columns={columns}
        // 数据
        data={data}
        // 初始化国际化语言  --- todo: i18n key doc address: https://github.com/KevinVandy/material-react-table/blob/main/packages/material-react-table/src/_locales/en.ts
        localization={tableI18n}
        muiLinearProgressProps={({ isTopToolbar }) => ({
          sx: { display: isTopToolbar ? "block" : "none" },
        })}
        // 多选底部提示
        positionToolbarAlertBanner={positionToolbarAlertBanner}
        // 开启action操作
        enableRowActions={enableRowActions}
        // action操作位置
        positionActionsColumn="last"
        // 修改底部 分页菜单样式
        paginationDisplayMode="mantine"
        // 手动分页
        manualPagination={true}
        editDisplayMode={"custom"}
        rowNumberMode="original"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: t("common.common_relatedOp"), //change header text
            size: actionWidth, //make actions column wider
          },
        }}
        renderRowActions={renderRowActions} // 添加这行
        //  渲染底部工具栏

        renderBottomToolbarCustomActions={({ table }) => {
          return (
            <ZKPagination
              totalRecords={rowCount}
              rowsPerPage={rowsPerPage}
              currentPage={currentPage}
              onPageChange={onPageChange}
              onPageSizeChange={onPageSizeChange}
            />
          );
        }}
        paginateExpandedRows={true}
        mantinePaginationProps={{
          rowsPerPageOptions: [5, 10, 20, 50, 100], // 每页行数选项
          withEdges: true, // 显示前后边缘的分页按钮
          radius: "xl", // 分页按钮圆角
          size: "lg", // 分页按钮尺寸
          showRowsPerPage: true, // 是否显示每页行数选项
        }}
        {...orther}
      />
    </React.Fragment>
  );
}

export default DataTable;

const NoResultFound = () => {
  const { t } = useTranslation();
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        background: `#F6F7FB 0% 0% no-repeat padding-box`,
      }}>
      <SvgIcon height="10em" width="10em" localIcon="NoResultFound"></SvgIcon>
      <span>{t("common.common_no_data")}</span>
    </div>
  );
};

const ZKPagination = ({
  totalRecords = 0,
  rowsPerPage = 10,
  currentPage = 0, // 默认从第0页开始
  onPageChange = () => {},
  onPageSizeChange = () => {},
}) => {
  const { t } = useTranslation();
  const totalPages = Math.ceil(totalRecords / rowsPerPage) || 0;

  const getPageDetails = () => {
    const startIndex = currentPage * rowsPerPage + 1;
    const endIndex = Math.min((currentPage + 1) * rowsPerPage, totalRecords);
    return `${startIndex}-${endIndex} of ${totalRecords}`;
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        // background: `#F6F7FB 0% 0% no-repeat padding-box`,
        width: "100%",
      }}>
      {/* 每页记录数选择器 */}
      <Box display="flex" alignItems="center">
        <InputLabel htmlFor="rowsPerPage">{t("每页记录数")}</InputLabel>
        <Select
          id="rowsPerPage"
          native
          value={rowsPerPage}
          onChange={(e) => onPageSizeChange(Number(e.target.value))}
          sx={{
            marginLeft: 1,
            "& .MuiNativeSelect-iconOutlined": {
              width: "1.6rem",
              height: "1.6rem",
            },
          }}>
          {[5, 10, 15, 20, 50].map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </Select>
      </Box>

      {/* 当前页详情 */}
      <Box flexGrow={1} pl={3}>
        <InputLabel>{getPageDetails()}</InputLabel>
      </Box>

      <Grid position={"absolute"} right={10}>
        <Pagination
          count={Number(totalPages) || 0}
          page={Number(currentPage) + 1} // 确保是数字并加1
          onChange={(e, page) => onPageChange(Number(page) - 1)} // 转换回 0 基索引
          variant="outlined"
          shape="rounded"
          // color="primary"
          sx={{
            "& .MuiPaginationItem-root": {
              backgroundColor: "#fff",
              color: "#637381",
            },
            "& .MuiPaginationItem-root.Mui-selected": {
              backgroundColor: "#E3E3E3",
              color: "#000",
            },
            "& .MuiPaginationItem-root:hover": {
              backgroundColor: "#ccc",
            },
          }}
        />
      </Grid>
    </Grid>
  );
};

ZKPagination.propTypes = {
  totalRecords: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
};
