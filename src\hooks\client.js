import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setClientCode, setClientId } from "@/store/reducers/client.js";

// 修复：将这些改为自定义 hooks
export const useClientId = () => useSelector((store) => store.client.clientId);

export const useClientCode = () =>
  useSelector((store) => store.client.clientCode);
export function useDispatchClient() {
  const dispatch = useDispatch();
  const stateSetClientId = useCallback(
    (id) => dispatch(setClientId(id)),
    [dispatch]
  );
  const stateSetClientCode = useCallback(
    (code) => dispatch(setClientCode(code)),
    [dispatch]
  );
  return { stateSetClientId, stateSetClientCode };
}
