import { timeZoneList } from "@/enums/TimeZone";
import ZKSearchTree from "@/pages/Organization/ZKSearchTree.jsx";
export const getFormConfig = (t, type, treeSelectRef) => {
  let formConfig = [
    {
      name: "name",
      label: t("outlets.name"),
      type: "input",
      required: true,
      placeholder: t("outlets.enter_outlet_name"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.outlet_name_required"),
        },
      ],
    },
    {
      codename: "countryCode",
      name: "phone",
      label: t("outlets.contracts_mobile"),
      type: "mobile",
      required: true,
      placeholder: t("outlets.enter_contracts_mobile"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.mobile_number_required"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("outlets.invalid_mobile_format"),
        },
      ],
    },
    {
      name: "email",
      label: t("outlets.contracts_email"),
      type: "input",
      required: true,
      placeholder: t("outlets.enter_contracts_email"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.contracts_email_required"),
        },
        {
          type: "email",
          message: t("outlets.invalid_email_format"),
        },
      ],
    },

    {
      name: "areaId",
      required: true,
      custom: true,
      label: t("outlets.outlet_location"),
      renderingCustomItem: (item, formik) => {
        return (
          <Grid xs={6} pl={3} mt={3}>
            <ZKSearchTree ref={treeSelectRef} formik={formik} {...item} />
          </Grid>
        );
      },

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.address_required"),
        },
      ],
    },

    {
      name: "timezone",
      label: t("outlets.time_zone"),
      type: "autoComplate",
      required: true,
      typevalue: "5",
      placeholder: t("outlets.select_time_zone"),
      options: timeZoneList,
      // disabled: type !== "editor",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.time_zone_required"),
        },
      ],
    },
    {
      name: "address",
      label: t("outlets.address"),
      type: "address",
      required: true,
      placeholder: t("outlets.enter_address"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("outlets.address_required"),
        },
      ],
    },
  ];

  return formConfig;
};
