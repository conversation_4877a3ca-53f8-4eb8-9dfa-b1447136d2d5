import React from "react";
import ZktecoTable from "@c/ZktecoTable/index";
import { getOutletList, deleteOutlet } from "@s/api/outlet";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { timeZoneList } from "@/enums/TimeZone";
import ZkTooltip from "@c/ZkTooltip";
import { toast } from "react-toastify";
import { getDepartmentId } from "@/pages/Organization/utils";
import { useConfirm } from "@/components/zkconfirm";
function TableList(props) {
  const { setRowCount, rowCount, data, setData } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const confirmFn = useConfirm();
  const [isError, setIsError] = useState(false);

  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const getTimeZone = (value, data) => {
    return data.find((item) => item.id == value);
  };

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("outlets.name"),
        enableClickToCopy: true, // 启用点击复制
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.name} arrow placement="bottom">
              <span>{e.row.original.name}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "address",
        header: t("outlets.address"),
        enableClickToCopy: true, // 启用点击复制
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.address} arrow placement="bottom">
              <span>{e.row.original.address}</span>
            </ZkTooltip>
          );
        },
      },

      {
        accessorKey: "timezone",
        header: t("outlets.time_zone"),
        Cell: (e) => {
          const timezone = e.row.original.timezone;
          return (
            <ZkTooltip
              title={getTimeZone(timezone, timeZoneList)?.name}
              arrow
              placement="bottom"
            >
              <span>{getTimeZone(timezone, timeZoneList)?.name}</span>
            </ZkTooltip>
          );
        },
      },

      {
        accessorKey: "email",
        header: t("outlets.organization_owner_email"),
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.email} arrow placement="bottom">
              <span>{e.row.original.email}</span>
            </ZkTooltip>
          );
        },
      },

      {
        accessorKey: "areaName",
        header: t("partner_user.area_name"),
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.areaName} arrow placement="bottom">
              <span>{e.row.original.areaName}</span>
            </ZkTooltip>
          );
        },
      },
    ],
    []
  );
  let departmentId = getDepartmentId();
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      parentId: departmentId,
    };

    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    await getOutletList(buildParams())
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  const handlerDetele = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("outlets.sure"),
    }).then(() => {
      deleteOutlet(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const isShowAction = {
    isShowView: "org:outlet:list",
    isShowEditor: "org:outlet:update",
    isShowDetele: "org:outlet:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/retail/outlet", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/add/retail/outlet", {
          state: { id: data?.id, type: "editor" },
        }),

      onAdd: (data) => {
        navigate("/add/retail/outlet", {
          state: { id: data?.id, type: "add" },
        });
      },

      Detele: (data) => {
        handlerDetele(data);
      },
    }),
    []
  );

  return (
    <React.Fragment>
      <ZktecoTable
        headerTitle={t("outlets.outlet_list")}
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={getTableData}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "org:outlet:save",
          onAdd: actionHandlers.onAdd,
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    </React.Fragment>
  );
}

export default TableList;
