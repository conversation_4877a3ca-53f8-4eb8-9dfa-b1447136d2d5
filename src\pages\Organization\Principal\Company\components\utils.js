import { getTreeSelect } from "@s/api/area";

export const handleUpload = (file, setImageUrl, setFileUrl) => {
  setFileUrl(file);
  const reader = new FileReader();
  reader.onload = (e) => {
    setImageUrl(e.target.result);
  };
  reader.readAsDataURL(file);
};

// 获取区域下拉选择树
export const getTreeList = (setTreeList) => {
  getTreeSelect().then((res) => {
    if (res?.code == "00000000") {
      setTreeList(res?.data);
    } else {
      setTreeList([]);
    }
  });
};




