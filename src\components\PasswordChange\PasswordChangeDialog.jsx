import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  InputAdornment,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
} from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  ArrowBack,
  Lock,
  Security,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import PasswordStrengthIndicator, {
  validatePassword,
  isPasswordStrong,
} from "./PasswordStrengthIndicator";
import { verifyPassword, changeUserPassword } from "@/service/api/user.js";
import { toast } from "react-toastify";

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: theme.spacing(2),
    minWidth: 400,
    maxWidth: 500,
  },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  paddingBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const PasswordChangeDialog = ({
  open = false,
  onClose,
  onPasswordChange,
  title,
  oldPasswordLabel,
  newPasswordLabel,
  confirmPasswordLabel,
  ...props
}) => {
  const { t } = useTranslation();

  const [step, setStep] = useState(0); // 0: 验证当前密码, 1: 设置新密码
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // 表单数据
  const [formData, setFormData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // 密码可见性控制
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // 表单验证错误
  const [fieldErrors, setFieldErrors] = useState({});

  const steps = [
    t("password_change.step_verify"),
    t("password_change.step_set_new"),
  ];

  // 重置表单
  const resetForm = () => {
    setFormData({
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
    setShowPassword({
      current: false,
      new: false,
      confirm: false,
    });
    setFieldErrors({});
    setError("");
    setSuccess("");
    setStep(0);
    setLoading(false);
  };

  // 关闭对话框
  const handleClose = () => {
    resetForm();
    onClose?.();
  };

  // 输入变化处理
  const handleInputChange = (field) => (event) => {
    const value = event.target.value;
    setFormData((prev) => ({ ...prev, [field]: value }));

    // 清除对应字段的错误
    if (fieldErrors[field]) {
      setFieldErrors((prev) => ({ ...prev, [field]: "" }));
    }
    setError("");
  };

  // 切换密码可见性
  const togglePasswordVisibility = (field) => {
    setShowPassword((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  // 验证当前密码
  const handleVerifyoldPassword = async () => {
    if (!formData.oldPassword.trim()) {
      setFieldErrors({
        oldPassword: t("password_change.error_current_required"),
      });
      return;
    }

    setLoading(true);
    setError("");

    try {
      // 调用验证函数
      const isValid = await verifyPassword?.({
        password: formData.oldPassword,
      });

      if (isValid) {
        setStep(1); // 进入下一步
        setSuccess(t("password_change.success_verified"));
      } else {
        setError(t("password_change.error_current_incorrect"));
        setFieldErrors({
          oldPassword: t("password_change.error_current_incorrect"),
        });
      }
    } catch (err) {
      setError(err.message || t("password_change.error_verify_failed"));
    } finally {
      setLoading(false);
    }
  };

  // 验证新密码
  const validateNewPassword = () => {
    const errors = {};

    // 使用密码强度验证
    const passwordErrors = validatePassword(
      formData.newPassword,
      formData.oldPassword,
      t
    );
    if (passwordErrors.length > 0) {
      errors.newPassword = passwordErrors[0]; // 显示第一个错误
    }

    // 检查密码强度
    if (formData.newPassword && !isPasswordStrong(formData.newPassword)) {
      errors.newPassword = t("password_change.error_password_weak");
    }

    if (!formData.confirmPassword.trim()) {
      errors.confirmPassword = t("password_change.error_confirm_required");
    } else if (formData.newPassword !== formData.confirmPassword) {
      errors.confirmPassword = t("password_change.error_passwords_not_match");
    }

    return errors;
  };

  // 提交新密码
  const handleSubmitNewPassword = async () => {
    const errors = validateNewPassword();

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const res = await changeUserPassword?.({
        oldPassword: formData.oldPassword,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      });

      setSuccess(t("common.password_change_success_changed"));
      toast.success(res?.message);
    } catch (err) {
      setError(err.message || t("common.password_change_error_change_failed"));
    } finally {
      setLoading(false);
      handleClose();
      onClose();
    }
  };

  // 返回上一步
  const handleBack = () => {
    setStep(0);
    setError("");
    setSuccess("");
    setFieldErrors({});
  };

  return (
    <StyledDialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      {...props}>
      <StyledDialogTitle>
        {step === 1 && (
          <IconButton onClick={handleBack} size="small" sx={{ mr: 1 }}>
            <ArrowBack />
          </IconButton>
        )}
        {step === 0 ? <Lock /> : <Security />}
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </StyledDialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* 步骤指示器 */}
        <Stepper activeStep={step} sx={{ mb: 3, mt: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* 错误和成功提示 */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Box component="form" noValidate>
          {step === 0 ? (
            // 第一步：验证当前密码
            <TextField
              fullWidth
              label={oldPasswordLabel}
              type={showPassword.current ? "text" : "password"}
              value={formData.oldPassword}
              onChange={handleInputChange("oldPassword")}
              error={!!fieldErrors.oldPassword}
              helperText={fieldErrors.oldPassword}
              disabled={loading}
              autoFocus
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => togglePasswordVisibility("current")}
                      edge="end">
                      {showPassword.current ? (
                        <VisibilityOff />
                      ) : (
                        <Visibility />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          ) : (
            // 第二步：设置新密码
            <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
              <Box>
                <TextField
                  fullWidth
                  label={newPasswordLabel}
                  type={showPassword.new ? "text" : "password"}
                  value={formData.newPassword}
                  onChange={handleInputChange("newPassword")}
                  error={!!fieldErrors.newPassword}
                  helperText={fieldErrors.newPassword}
                  disabled={loading}
                  autoFocus
                  inputProps={{
                    maxLength: 64, // 限制最大长度为64位
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => togglePasswordVisibility("new")}
                          edge="end">
                          {showPassword.new ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                {/* 密码强度指示器 */}
                <Box mt={2}>
                  <PasswordStrengthIndicator
                    password={formData.newPassword}
                    showRequirements={true}
                    showScore={false}
                  />
                </Box>
              </Box>

              <TextField
                fullWidth
                label={confirmPasswordLabel}
                type={showPassword.confirm ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={handleInputChange("confirmPassword")}
                error={!!fieldErrors.confirmPassword}
                helperText={fieldErrors.confirmPassword}
                disabled={loading}
                inputProps={{
                  maxLength: 64, // 限制最大长度为64位
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => togglePasswordVisibility("confirm")}
                        edge="end">
                        {showPassword.confirm ? (
                          <VisibilityOff />
                        ) : (
                          <Visibility />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 2 }}>
        <Button onClick={handleClose} disabled={loading} color="inherit">
          {t("password_change.btn_cancel")}
        </Button>

        <Button
          onClick={
            step === 0 ? handleVerifyoldPassword : handleSubmitNewPassword
          }
          variant="contained"
          disabled={loading}
          startIcon={loading && <CircularProgress size={16} />}>
          {loading
            ? t("password_change.btn_processing")
            : step === 0
            ? t("password_change.btn_verify")
            : t("password_change.btn_confirm")}
        </Button>
      </DialogActions>
    </StyledDialog>
  );
};

export default PasswordChangeDialog;
