import React, { useState, forwardRef, useEffect } from "react";
import SearchTree from "@/components/AntdTreeSelect/index";
import { treeList, subTreeList } from "@/service/api/area";
import RequirePoint from "@c/RequirePoint";
import i18n from "i18next";
const ZKSearchTree = forwardRef((props, ref) => {
  const {
    label,
    onChange,
    placeholder = i18n.t("common.common_please_select") + " " + `${label}`,
    isContainOldData = "0",
    regionKey,
    onClear,
    labelpostion,
    formik = null,
    name, // formik 字段名
    required = false,
  } = props;
  const [treeData, setTreeData] = useState([]);

  // 从 localStorage 获取持久化的值
  const getStoredValue = () => {
    if (!regionKey) return { id: "", name: "" };
    try {
      const stored = localStorage.getItem(regionKey);
      return stored ? JSON.parse(stored) : { id: "", name: "" };
    } catch (error) {
      return { id: "", name: "" };
    }
  };

  const storedValue = getStoredValue();
  const [valueId, setValueId] = useState(storedValue.id);
  const [valueName, setValueName] = useState(storedValue.name);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [loadingKeys, setLoadingKeys] = useState([]);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = () => {
    treeList({ isContainOldData: isContainOldData }).then((res) => {
      if (res?.code == "00000000") {
        // 为初始数据设置 isLeaf 属性，假设所有一级节点都可能有子节点
        const processedData = res.data.map((node) => ({
          ...node,
          isLeaf: false, // 一级节点默认不是叶子节点，显示折叠图标
          subRows: node.subRows || [], // 确保有 subRows 属性
        }));
        setTreeData(processedData);
      } else {
        setTreeData([]);
      }
    });
  };

  const loadData = async (node) => {
    // 防止重复加载
    if (loadingKeys.includes(node.id)) {
      return Promise.resolve();
    }

    // 检查节点是否已经有子数据，如果有则不需要重新加载
    if (node.subRows && node.subRows.length > 0) {
      return Promise.resolve();
    }

    setLoadingKeys((prev) => [...prev, node.id]);

    return new Promise((resolve) => {
      subTreeList(node.id)
        .then((res) => {
          if (res?.code == "00000000") {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, res.data, false)
            );
          } else {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, [], false)
            );
          }
        })
        .catch(() => {
          setTreeData((prevTreeData) =>
            updateTreeData(prevTreeData, node.id, [], false)
          );
        })
        .finally(() => {
          setLoadingKeys((prev) => prev.filter((key) => key !== node.id));
          resolve();
        });
    });
  };

  const updateTreeData = (list, key, children, forceLeaf = true) => {
    return list.map((node) => {
      if (node.id === key) {
        // 为新加载的子节点设置 isLeaf 属性
        const processedChildren = children.map((child) => ({
          ...child,
          isLeaf: false, // 假设子节点也可能有子节点，显示折叠图标
          subRows: child.subRows || [],
        }));

        return {
          ...node,
          subRows: processedChildren,
          isLeaf: forceLeaf ? processedChildren.length === 0 : false,
        };
      }
      if (node.subRows) {
        return {
          ...node,
          subRows: updateTreeData(node.subRows, key, children, forceLeaf),
        };
      }
      return node;
    });
  };

  // 清除选中的值
  const clearItem = () => {
    setValueId("");
    setValueName("");
    clearStorage();
    onClear?.(); // 调用外部传入的清除回调
  };

  React.useImperativeHandle(ref, () => ({
    setItem,
    clearItem,
  }));

  const setItem = (item) => {
    const id = item?.id || "";
    const label = item?.name || "";

    setValueId(id);
    setValueName(label);
    formik.setFieldValue(name, id);

    // 保存到 localStorage
    if (id && label) {
      saveToStorage(id, label);
    } else {
      clearStorage();
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <SearchTree
            treeData={treeData}
            fieldNames={{
              label: "name",
              value: "id",
              children: "subRows",
            }}
            virtual={true}
            showSearch={true}
            allowClear={!!(valueId && valueName)} // 只有当有值时才显示清除图标
            treeLine={true}
            treeDefaultExpandAll={false}
            size="medium"
            virtualThreshold={100}
            virtualItemSize={40}
            maxTagCount={3}
            loadData={loadData} // 恢复 loadData 以显示折叠图标
            // 尝试不同的value格式
            value={{ value: valueId, label: valueName }} // 使用对象格式
            labelInValue={true}
            placeholder={placeholder}
            onTreeExpand={(newExpandedKeys, info) => {
              // 更新展开状态
              setExpandedKeys(newExpandedKeys);
            }}
            treeExpandedKeys={expandedKeys}
            onChange={(valObj, node) => {
              if (valObj?.value && valObj?.label) {
                setValueId(valObj.value);
                setValueName(valObj.label);
                formik.setFieldValue(name, valObj.value);
                // 保存到 localStorage
                saveToStorage(valObj.value, valObj.label);

                onChange?.({
                  id: valObj.value,
                  name: valObj.label,
                  location: valObj?.location,
                  ...node,
                });
              } else {
                setValueId("");
                setValueName("");
                formik.setFieldValue(name, "");
                // 清除 localStorage
                clearStorage();

                onChange?.(undefined);
              }
            }}
            onSelect={(value, node) => {
              if (value && node) {
                setValueId(value);
                setValueName(node.name);
                formik.setFieldValue(name, value);
                // 保存到 localStorage
                saveToStorage(value, node.name);

                // 手动关闭下拉框
                setTimeout(() => {
                  const event = new MouseEvent("click", {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                  });
                  document.body.dispatchEvent(event);
                }, 100);

                onChange?.({
                  id: value,
                  name: node.name,
                  ...node,
                });
              }
            }}
            // 确保选择后关闭下拉框
            dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
            // 添加更多属性尝试解决问题
            showArrow={true}
            treeNodeFilterProp="name"
            treeNodeLabelProp="name"
          />

          {/* 显示 formik 验证错误 */}
          {formik && name && formik.touched[name] && formik.errors[name] && (
            <FormHelperText error sx={{ mt: 1 }}>
              {formik.errors[name]}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
});

export default ZKSearchTree;
