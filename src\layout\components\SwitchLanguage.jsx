import React, { useState } from "react";
import Typography from "@mui/material/Typography";
import Popover from "@mui/material/Popover";
import { Box, Tooltip, MenuItem } from "@mui/material";
import {
  usePopupState,
  bindTrigger,
  bindPopover,
} from "material-ui-popup-state/hooks";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import Language from "@/assets/menuIcon/Language.svg?react";
import { useTranslation } from "react-i18next";
import { setStoreLang } from "@/utils/langUtils";
import { updateLanguage } from "@/utils/actions";
import { triggerLanguageChange } from "@/utils/microAppI18nSync";
import SvgIcon from "@/components/SvgIcon";

export default function SwitchLanguage() {
  const { t } = useTranslation();
  const { i18n } = useTranslation();
  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });

  const switchLang = (lang) => {
    try {
      console.log(`🌐 切换语言到: ${lang}`);

      // 1. 更新主应用的国际化
      i18n.changeLanguage(lang);

      // 2. 保存到浏览器存储
      setStoreLang(lang);

      // 3. 同步更新微前端全局状态
      updateLanguage(lang);

      // 4. 触发语言变化事件，通知所有子应用
      triggerLanguageChange(lang);

      // 5. 检查当前是否在微前端应用中
      const currentPath = window.location.pathname;
      const isMicroApp =
        currentPath.startsWith("/cms-app") ||
        currentPath.startsWith("/retail-ai-app") ||
        currentPath.startsWith("/e-price-tag-app");

      if (isMicroApp) {
        console.log("🔄 在微前端应用中切换语言，将刷新页面以确保同步");
        // 在微前端应用中，需要刷新以确保子应用正确更新
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        console.log("✅ 在主应用中切换语言，无需刷新页面");
        // 在主应用中，不需要刷新页面
        // 触发自定义事件通知组件重新渲染
        window.dispatchEvent(
          new CustomEvent("main-app-language-changed", {
            detail: { language: lang },
          })
        );
      }
    } catch (error) {
      console.error("❌ 语言切换失败:", error);
      // 如果出错，回退到刷新页面
      window.location.reload();
    }
  };
  return (
    <div>
      <Box
        className="flex items-center justify-between h-[40px] px-4 py-1 hover:bg-gray-100 focus:bg-gray-100 rounded-md cursor-pointer"
        {...bindTrigger(popupState)}>
        <div className="flex gap-4 items-center">
          <SvgIcon
            color={"#a1a4a6"}
            height="1.2em"
            icon="carbon:ibm-watson-language-translator"
          />
          <Tooltip title={"Language"} arrow placement="bottom">
            <div className="text-gray-400 text-[14px]">
              {t("common.common_language")}
            </div>
          </Tooltip>
        </div>

        <Box>
          <div>
            {popupState.isOpen ? (
              <KeyboardArrowLeftIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            ) : (
              <KeyboardArrowRightIcon
                fontSize="small"
                style={{
                  color: `#A2A3A3`,
                }}
              />
            )}
          </div>
        </Box>
      </Box>
      <Popover
        {...bindPopover(popupState)}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        sx={{
          "& .MuiPaper-root": {
            width: "200px",
            // minHeight: 160,
          },
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}>
        <div className="my-1 mx-1">
          <MenuItem
            className="text-[14px] text-gray-500"
            onClick={() => {
              switchLang("en");
              popupState.close();
            }}>
            {t("English")}
          </MenuItem>
          <MenuItem
            className="text-[14px] text-gray-500"
            onClick={() => {
              switchLang("zh");
              popupState.close();
            }}>
            {t("中文(简体)")}
          </MenuItem>
        </div>

        {/* <MenuItem
          onClick={() => {
            switchLang("th");
            popupState.close();
          }}>
          {t("Español")}
        </MenuItem>
        <MenuItem
          onClick={() => {
            switchLang("jp");
            popupState.close();
          }}>
          {t("日本語")}
        </MenuItem>

        <MenuItem
          onClick={() => {
            switchLang("pt");
            popupState.close();
          }}>
          {t("Português")}
        </MenuItem> */}
      </Popover>
    </div>
  );
}
