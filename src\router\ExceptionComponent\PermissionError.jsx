import React from "react";
import {
  Box,
  Typography,
  Button,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";
import {
  Lock,
  Home,
  ContactSupport,
  Security,
  Person,
  AdminPanelSettings,
  Check,
  Close,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

const shake = keyframes`
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// 主容器
const MainContainer = styled(Box)(({ theme }) => ({
  minHeight: "100vh",
  background: `linear-gradient(135deg, 
    ${theme.palette.warning.light}20 0%, 
    ${theme.palette.background.default} 100%)`,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: theme.spacing(2),
}));

// 内容卡片
const ContentCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(6, 4),
  borderRadius: theme.spacing(3),
  textAlign: "center",
  maxWidth: 700,
  width: "100%",
  animation: `${fadeIn} 0.8s ease-out`,
}));

// 锁图标
const LockIcon = styled(Lock)(({ theme }) => ({
  fontSize: "6rem",
  color: theme.palette.warning.main,
  marginBottom: theme.spacing(2),
  animation: `${shake} 2s ease-in-out infinite`,
}));

const PermissionError = ({
  title = "访问被拒绝",
  message = "您没有权限访问此资源",
  requiredPermissions = [],
  userPermissions = [],
  contactInfo,
  showPermissionDetails = true,
  errorCode = "403",
  ...props
}) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleGoHome = () => {
    navigate("/dashboard");
  };

  const handleContactSupport = () => {
    if (contactInfo?.email) {
      window.location.href = `mailto:${
        contactInfo.email
      }?subject=权限申请&body=我需要申请以下权限访问：${requiredPermissions.join(
        ", "
      )}`;
    } else if (contactInfo?.phone) {
      window.location.href = `tel:${contactInfo.phone}`;
    } else {
      // 默认联系方式
      alert("请联系系统管理员申请相关权限");
    }
  };

  const handleLogin = () => {
    navigate("/login");
  };

  // 检查用户是否有特定权限
  const hasPermission = (permission) => {
    return userPermissions.includes(permission);
  };

  // 获取权限状态
  const getPermissionStatus = (permission) => {
    return hasPermission(permission) ? "granted" : "denied";
  };

  const permissionSuggestions = [
    "联系系统管理员申请相关权限",
    "确认您的账户角色是否正确",
    "检查是否需要重新登录",
    "确认访问的资源路径是否正确",
  ];

  return (
    <MainContainer {...props}>
      <Container maxWidth="md">
        <ContentCard elevation={3}>
          {/* 错误代码 */}
          <Chip
            label={`错误代码: ${errorCode}`}
            color="warning"
            variant="outlined"
            sx={{ mb: 2 }}
          />

          {/* 主图标 */}
          <LockIcon />

          {/* 标题 */}
          <Typography
            variant={isMobile ? "h4" : "h3"}
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: "text.primary",
              mb: 2,
            }}>
            {title}
          </Typography>

          {/* 描述 */}
          <Typography
            variant="h6"
            color="text.secondary"
            paragraph
            sx={{
              mb: 4,
              lineHeight: 1.6,
              maxWidth: 500,
              mx: "auto",
            }}>
            {message}
          </Typography>

          {/* 权限详情 */}
          {showPermissionDetails && requiredPermissions.length > 0 && (
            <Box sx={{ mb: 4 }}>
              <Alert severity="warning" sx={{ mb: 3, textAlign: "left" }}>
                <AlertTitle>所需权限</AlertTitle>
                <List dense>
                  {requiredPermissions.map((permission, index) => (
                    <ListItem key={index} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {getPermissionStatus(permission) === "granted" ? (
                          <Check color="success" />
                        ) : (
                          <Close color="error" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={permission}
                        secondary={
                          getPermissionStatus(permission) === "granted"
                            ? "已授权"
                            : "未授权"
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Alert>
            </Box>
          )}

          {/* 操作按钮 */}
          <Box
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "center",
              flexWrap: "wrap",
              mb: 4,
            }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Home />}
              onClick={handleGoHome}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
              }}>
              返回首页
            </Button>

            {/* <Button
              variant="outlined"
              size="large"
              startIcon={<ContactSupport />}
              onClick={handleContactSupport}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
              }}>
              申请权限
            </Button> */}

            <Button
              variant="outlined"
              size="large"
              startIcon={<Person />}
              onClick={handleLogin}
              sx={{
                borderRadius: 3,
                px: 4,
                py: 1.5,
              }}>
              重新登录
            </Button>
          </Box>

          {/* 建议步骤 */}
          <Box sx={{ mt: 4 }}>
            <Alert severity="info" sx={{ textAlign: "left" }}>
              <AlertTitle>解决建议</AlertTitle>
              <Box component="ol" sx={{ pl: 2, m: 0 }}>
                {permissionSuggestions.map((suggestion, index) => (
                  <Typography
                    key={index}
                    component="li"
                    variant="body2"
                    sx={{ mb: 0.5 }}>
                    {suggestion}
                  </Typography>
                ))}
              </Box>
            </Alert>
          </Box>

          {/* 用户信息 */}
          {userPermissions.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                当前用户权限:
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  flexWrap: "wrap",
                  justifyContent: "center",
                }}>
                {userPermissions.map((permission, index) => (
                  <Chip
                    key={index}
                    label={permission}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* 联系信息 */}
          {contactInfo && (
            <Box
              sx={{ mt: 4, p: 2, backgroundColor: "grey.50", borderRadius: 2 }}>
              <Box
                sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}>
                <AdminPanelSettings color="primary" />
                <Typography variant="subtitle2">系统管理员联系方式</Typography>
              </Box>
              {contactInfo.email && (
                <Typography variant="body2" color="text.secondary">
                  邮箱: {contactInfo.email}
                </Typography>
              )}
              {contactInfo.phone && (
                <Typography variant="body2" color="text.secondary">
                  电话: {contactInfo.phone}
                </Typography>
              )}
            </Box>
          )}

          {/* 安全提示 */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mt: 4,
              opacity: 0.7,
              fontSize: "0.875rem",
            }}>
            <Security
              fontSize="small"
              sx={{ verticalAlign: "middle", mr: 0.5 }}
            />
            为了系统安全，某些资源需要特定权限才能访问
          </Typography>
        </ContentCard>
      </Container>
    </MainContainer>
  );
};

export default PermissionError;
