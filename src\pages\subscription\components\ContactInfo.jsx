import React, { useEffect, useState } from "react";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import PublishIcon from "@mui/icons-material/Publish";
import { useTranslation } from "react-i18next";
import { getContactConfig } from "../js/Config";
import { useLocation } from "react-router-dom";
import dayjs from "dayjs";
import CustomCard from "./CustomCard";
import FileUpload from "@c/FileUpload";
import { toast } from "react-toastify";
import { getUnitList } from "@/service/api/subscription.js";
function ContactInfo(props) {
  const { formik, contactInfo, setContactInfo } = props;

  const { t } = useTranslation();
  const { state } = useLocation();
  const [unitList, setUnitList] = useState([]);

  useEffect(() => {
    getUnitList().then((res) => {
      setUnitList(res?.data);
    });
  }, []);

  useEffect(() => {
    setContactInfo(getContactConfig(t, state, unitList));
  }, [unitList]);

  return (
    <CustomCard title={t("subscription.contract_information")}>
      <ZkFormik sx={6} formik={formik} formConfig={contactInfo}>
        <Grid item xs={6}>
          <Grid
            sx={{
              marginTop: "30px",
              display: "flex",
              justifyContent: "flex-start",
            }}>
            {formik.values.attachmentFile ? (
              <>
                <a
                  sx={{
                    marginTop: "30px",
                    display: "flex",
                    justifyContent: "flex-start",
                    textDecoration: "none",
                    color: "#595959",
                  }}
                  target="_blank"
                  // href={formik.values.attachmentFile}
                  onClick={(e) => {
                    e.preventDefault();
                    if (formik.values.attachmentFile) {
                      const file = formik.values.attachmentFile;
                      const blob = new Blob([file], { type: file.type });
                      const url = window.URL.createObjectURL(blob);
                      const link = document.createElement("a");
                      link.href = url;
                      link.download = file.name || "contract_file";
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      window.URL.revokeObjectURL(url);
                    }
                  }}
                  rel="noreferrer">
                  {t("subscription.contractFile")}
                </a>
                <DeleteForeverIcon
                  onClick={() => {
                    formik.setFieldValue("attachmentFile", "");
                  }}></DeleteForeverIcon>
              </>
            ) : (
              <FileUpload
                accept={{
                  "application/pdf": [".pdf"],
                  "application/msword": [".doc"],
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    [".docx"],
                  "image/jpeg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                }}
                onUpload={(data) => {
                  let file = data[0];
                  let fileSize = file.size;
                  if (fileSize > 1024 * 1024 * 30) {
                    toast.error(
                      t("subscription.limtFileSize", {
                        tip: "30M",
                      })
                    );
                    return false;
                  }

                  formik.setFieldValue("attachmentFile", data[0]);
                }}>
                <Grid
                  sx={{
                    height: "40px",
                    display: "flex",
                    alignItems: "center",
                  }}>
                  {t("subscription.uploadContract")}
                  <PublishIcon></PublishIcon>
                </Grid>
              </FileUpload>
            )}

            {formik.touched.attachmentFile && formik.errors.attachmentFile && (
              <FormHelperText error id="name-error">
                {formik.errors.attachmentFile}
              </FormHelperText>
            )}
          </Grid>
        </Grid>
      </ZkFormik>
    </CustomCard>
  );
}

export default ContactInfo;
