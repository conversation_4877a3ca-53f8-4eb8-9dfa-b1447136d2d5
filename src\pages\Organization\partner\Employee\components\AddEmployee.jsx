import React, { useEffect, useState } from "react";
import { Grid, Divider, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import CustomUpload from "@l/MenuItem/components/CustomUpload";
import { getBranchDetail, getFormConfig } from "./utils";
import {
  getDepartmentId,
  handleUpload,
  getSelectList,
} from "@/pages/Organization/utils.js";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { createValidation } from "@c/Config/validationUtils.js";
import { useFormik } from "formik";
import { addParnerUser, editParnerUser } from "@s/api/partner";
import { useNavigate, useLocation } from "react-router-dom";

import { toast } from "react-toastify";
import RolesList from "@/pages/Organization/RolesList";
function AddEmployee(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);
  const [selectRole, setSelectRole] = useState([]);
  const [detailData, setDetailData] = useState([]);
  const [formConfig, setFormConfig] = useState([]);
  const [permissionList, setPermissionList] = useState([]);
  const departmentId = getDepartmentId();
  const isEditing = state?.type == "editor" || state?.type == "view";

  useEffect(() => {
    //  获取 租户用户详情
    if (isEditing) {
      getBranchDetail(state?.id, setDetailData, setImageUrl, setSelectRole);
    }

    // 获取数据权限列表数据
    getSelectList(setPermissionList, departmentId);
  }, [isEditing, state?.id]);

  const initialValues = isEditing
    ? {
        id: detailData?.id,
        email: detailData?.email,
        firstName: detailData?.firstName,
        lastName: detailData?.lastName,
        countryCode: detailData?.countryCode,
        phone: detailData?.phone,
        dataScopeId: detailData?.dataScopeId,
        departmentId: departmentId,
      }
    : {
        email: "",
        firstName: "",
        lastName: "",
        countryCode: "",
        phone: "",
        // password: "",
        // confirmPassword: "",
        dataScopeId: "",
        departmentId: departmentId,
      };
  useEffect(() => {
    const formConfig = getFormConfig(t, state?.type, permissionList);
    setFormConfig(formConfig);
  }, [permissionList]);

  const appendRoleIds = useCallback((formData, roles) => {
    if (Array.isArray(roles)) {
      roles.forEach((role) => {
        if (typeof role === "object" && role.id) {
          formData.append("roleIds", role.id);
        } else if (typeof role === "string" || typeof role === "number") {
          formData.append("roleIds", role);
        }
      });
    } else if (typeof roles === "object" && roles !== null) {
      Object.values(roles).forEach((role) => {
        formData.append("roleIds", role);
      });
    }
  }, []);

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: async (values) => {
      // if (!values.countryCode) {
      //   toast.error(t("Please Select Country Code"));
      //   return;
      // }

      if (!values.dataScopeId) {
        toast.error(t("partner.data_permission_required"));
        return;
      }

      if (!selectRole || selectRole.length === 0) {
        toast.error(t("roles.authorization_level_placeholder"));
        return;
      }

      const formData = new FormData();
      if (fileUrl) {
        formData.append("multipartFile", fileUrl);
      }

      appendRoleIds(formData, selectRole);

      Object.entries(values).forEach(([key, value]) => {
        formData.append(key, value);
      });

      setLoading(true);

      try {
        const res = await (isEditing
          ? editParnerUser(formData)
          : addParnerUser(formData));
        toast.success(res?.message);
        navigate("/partner/employee/list");
      } catch (error) {
        toast.error(res?.message);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/partner/employee/list"}
        title={
          state?.type === "editor"
            ? t("Edit Partner Employee")
            : t("Add Partner Employee")
        }
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/partner/employee/list");
        }}
        loading={loading}>
        <Grid item xs={12} sm={12} md={12} m={4} p={2}>
          <CustomUpload
            imageUrl={imageUrl}
            setImageUrl={setImageUrl}
            handleUpload={(file) =>
              handleUpload(file, setImageUrl, setFileUrl)
            }></CustomUpload>
        </Grid>
        <Grid container xs={12} pl={6} pr={6}>
          <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
          <RolesList
            id={departmentId}
            initialRoles={selectRole || []}
            onChange={(e) => setSelectRole(e)}></RolesList>
        </Grid>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddEmployee;
