import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import { useTranslation } from "react-i18next";
import { getParnerList } from "@s/api/partner";
import { useTableRequest } from "../../utils.js";
const index = () => {
  const { t } = useTranslation();

  const [serchName, setSeachName] = useState("");

  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getParnerList);

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset();
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        pagination={pagination}
        setPagination={setPagination}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("partner.title")}
        isSearch={true}
        onClick={handlerSeacher}
        serchName={serchName}
        setSeachName={setSeachName}
        onClear={handleClear} //
        content={rederTable()}></LayoutList>
    </React.Fragment>
  );
};

export default index;
