import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bootstrap<PERSON>ontent,
  <PERSON>trapA<PERSON>,
  BootstrapDialogTitle,
} from "@/components/dialog";
import { useTranslation } from "react-i18next";
import SvgIcon from "@/components/SvgIcon";
import { useStateUserInfo } from "@/hooks/user.js";
import {
  Typography,
  Button,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  Divider,
  Box,
  Alert,
  Chip,
} from "@mui/material";
import PasswordChangeDialog from "@/components/PasswordChange/PasswordChangeDialog";
import { VpnKey, Security, Shield } from "@mui/icons-material";
import { switchTfa } from "@/service/api/user.js";
function SecuritySettings({ open, setOpen }) {
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [twoFactorLoading, setTwoFactorLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 加载用户数据
  useEffect(() => {
    if (userInfo) {
      // 初始化双因子登录状态
      setTwoFactorEnabled(userInfo?.tfaFlag);
    }
  }, [userInfo]);

  // 处理双因子登录开关
  const handleTwoFactorToggle = useCallback(async (enabled) => {
    setTwoFactorLoading(true);
    try {
      let params = {
        tfaFlag: enabled,
      };
      // 这里调用API来启用/禁用双因子登录
      const response = await switchTfa(params);

      if (response?.code == "00000000") {
        setTwoFactorEnabled(enabled);
        toast.success(
          enabled
            ? t("security.two_factor_enabled")
            : t("security.two_factor_disabled")
        );
      }
    } finally {
      setTwoFactorLoading(false);
    }
  }, []);

  return (
    <React.Fragment>
      <BootstrapDialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={() => setOpen(false)}
        aria-labelledby="customized-dialog-title">
        <BootstrapDialogTitle onClose={() => setOpen(false)}>
          <Grid container>
            <Security color="primary" />
            <Typography
              variant="h4"
              component="p"
              style={{
                marginLeft: "10px",
              }}>
              {t("common.common_security")}
            </Typography>
          </Grid>
        </BootstrapDialogTitle>
        <BootstrapContent dividers={true}>
          <Box className="space-y-6">
            <Card variant="outlined">
              <CardContent>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between">
                  <Box display="flex" alignItems="center" gap={2}>
                    <VpnKey color="action" />
                    <Box>
                      <Typography variant="subtitle1" fontWeight="500">
                        {t("common.common_change_password")}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t("password.security_account")}
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="contained"
                    onClick={() => setDialogOpen(true)}
                    sx={{
                      background:
                        "linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
                      "&:hover": {
                        background:
                          "linear-gradient(270deg, #1276B8 0%, #6BA322 100%)",
                      },
                    }}>
                    {t("password.change_password")}
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* 双因子登录 */}

            <Card variant="outlined">
              <CardContent>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between">
                  <Box display="flex" alignItems="center" gap={2}>
                    <SvgIcon
                      icon="mdi:security"
                      height="1.5em"
                      className="text-gray-400"
                    />
                    <Box>
                      <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                        <Typography variant="subtitle1" fontWeight="500">
                          {t("security.two_factor_auth_title")}
                        </Typography>
                        {twoFactorEnabled && (
                          <Chip
                            label={t("security.two_factor_enabled_label")}
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {t("security.two_factor_description")}
                      </Typography>
                      {twoFactorEnabled && (
                        <Alert severity="info" sx={{ mt: 1, py: 0.5 }}>
                          {t("security.two_factor_login_notice")}
                        </Alert>
                      )}
                    </Box>
                  </Box>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={twoFactorEnabled}
                        onChange={(e) =>
                          handleTwoFactorToggle(e.target.checked)
                        }
                        disabled={twoFactorLoading}
                        color="primary"
                      />
                    }
                    label=""
                    sx={{ m: 0 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Box>
        </BootstrapContent>
        <BootstrapActions></BootstrapActions>
      </BootstrapDialog>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        title={t("common.common_change_password")}
        currentPasswordLabel={t("common.current_password")}
        newPasswordLabel={t("common.new_password")}
        confirmPasswordLabel={t("common.common_confirm_password")}
      />
    </React.Fragment>
  );
}

export default SecuritySettings;
