/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState, useMemo, useRef } from "react";
import Box from "@mui/material/Box";
import {
  TextField,
  FormControl,
  OutlinedInput,
  IconButton,
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import SearchIcon from "@mui/icons-material/Search";
import axios from "axios";
import { getAddress } from "@/service/api/bmap";
import { useTranslation } from "react-i18next";
const mapTextAutoComplete = (props) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);

  const loaded = useRef(false);
  const autocompleteService = useRef(null);

  const fetch = React.useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService.current(request, callback);
      }, 400),
    []
  );
  useEffect(() => {
    let active = true;
    if (!autocompleteService.current) {
      autocompleteService.current = (request, callback) => {
        getAddress(request.query, request.region)
          .then((res) => {
            callback(res.data);
          })
          .catch(function (error) {
            // callback([]);
            console.log(error);
          });
        // axios
        //     .get(request.url)
        //     .then(function (response) {
        //         if (response.data.result) {
        //             callback(response.data.result);
        //         } else {
        //             callback([]);
        //         }
        //     })
        // .catch(function (error) {
        //     console.log(error);
        // })
        //     .finally(function () {
        //         console.log('error');
        //     });
      };
    }

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return undefined;
    }
    // let requestUrl = `/baidu/?query=${inputValue}&region=${props.code}&city_limit=false&output=json&ak=APeE0SHYyqFcNp2bRlrSiKniDUZDaTMA`;
    fetch({ query: inputValue, region: props.code }, (results) => {
      if (active) {
        setOptions(results);
      }
    });
    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);

  return (
    <Autocomplete
      id="baidu-map-search"
      sx={props.sx}
      getOptionLabel={(option) =>
        typeof option === "string" ? option : option.name
      }
      filterOptions={(x) => x}
      style={{ width: "325px", marginTop: "0px" }}
      options={options}
      autoComplete
      includeInputInList
      filterSelectedOptions
      value={value}
      noOptionsText={t("common.common_input_location_search")}
      onChange={(event, newValue) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
        props.setCenter(newValue);
      }}
      onInputChange={(event, newInputValue) => {
        // 出发搜索
        setInputValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          placeholder={props.placeholder}
          {...params}
          label=""
          size="small"
          sx={{ width: 325, marginTop: "1px", label: { marginTop: 0.25 } }}
          fullWidth
        />
      )}
      renderOption={(props, option) => {
        return (
          <li {...props}>
            <Grid container alignItems="center">
              <Grid item sx={{ display: "flex", width: 44 }}>
                <LocationOnIcon sx={{ color: "text.secondary" }} />
              </Grid>
              <Grid
                item
                sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}>
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  {option.name}
                </Box>
              </Grid>
            </Grid>
          </li>
        );
      }}
    />
  );
};
export default mapTextAutoComplete;
