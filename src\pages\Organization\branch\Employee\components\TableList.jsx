import React from "react";
import ZktecoTable from "@c/ZktecoTable";
import { useNavigate } from "react-router-dom";
import { deleteTenantUser } from "@s/api/tenant";
import { toast } from "react-toastify";
import { useConfirm } from "@/components/zkconfirm";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  setOpen,
  setTenantId,
  getTableData,
  departmentCode,
}) {
  const { t } = useTranslation();
  const confirmFn = useConfirm();
  const navigate = useNavigate();

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "logo",
        header: t("branch_user.logo"),
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Avatar
            src={row.original.photo}
            alt={"Logo"}
            size="medium"
            sx={{
              width: "121px",
              height: "50px",
              borderRadius: "8px",
              border: "1px solid #E3E3E3",
            }}
          />
        ),
      },
      {
        accessorKey: "firstName",
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        header: t("branch_user.firstName"),
      },
      {
        accessorKey: "lastName",
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        header: t("branch_user.lastName"),
      },
      {
        accessorKey: "email",
        enableGlobalFilter: true,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        header: t("branch_user.email"),
      },
      {
        accessorKey: "phoneDetails",
        header: t("branch_user.phone"),
        enableGlobalFilter: false,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => {
          const countryCode = row.original.countryCode || "";
          const phone = row.original.phone || "";
          return `+${countryCode} ${phone}`.trim();
        },
      },
      {
        accessorKey: "roleNames",
        header: t("branch_user.roleNames"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },

      // {
      //   accessorKey: "dataScope",
      //   header: t("data_permission.add_permission"),
      //   enableClickToCopy: true, // 启用点击复制
      //   size: 250,
      // },
    ],
    []
  );

  const isShowAction = {
    isShowView: "org:tenant_employee:list",
    isShowEditor: "org:tenant_employee:update",
    isShowDetele: "org:tenant_employee:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  // 删除
  const handlerDetele = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("common.common_confirm_delete"),
    }).then(() => {
      deleteTenantUser(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/branch/employee", {
          state: { id: data?.id, type: "view" },
        }),
      handlerEditor: (data) =>
        navigate("/add/branch/employee", {
          state: { id: data?.id, type: "editor" },
        }),

      Detele: (data) => {
        handlerDetele(data);
      },
    }),
    [navigate, setOpen, setTenantId]
  );

  return (
    <React.Fragment>
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        pathRoute="/add/branch/employee"
        loadDada={getTableData}
        enableRowActions={
          departmentCode == sessionStorage.getItem("tenantCode")
        }
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "org:tenant_employee:save",
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    </React.Fragment>
  );
}

export default TableList;
