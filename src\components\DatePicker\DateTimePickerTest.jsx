import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  FormLabel,
  Stack,
  Chip,
  Button,
  Alert,
  AlertTitle,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import DateTimePicker from './DateTimePicker';
import dayjs from 'dayjs';

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[2],
  transition: theme.transitions.create(['box-shadow', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  },
}));

const ResultChip = styled(Chip)(({ theme }) => ({
  fontFamily: 'monospace',
  fontSize: '0.75rem',
  maxWidth: '100%',
  '& .MuiChip-label': {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
}));

const DateTimePickerTest = () => {
  const [value1, setValue1] = useState();
  const [value2, setValue2] = useState(dayjs());
  const [value3, setValue3] = useState();

  return (
    <Box sx={{ p: 3, backgroundColor: 'background.default', minHeight: '100vh' }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
          DateTimePicker 测试
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          既可以选择年月日又能选择时分秒的时间组件
        </Typography>
        <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto' }}>
          <AlertTitle>组件特性</AlertTitle>
          左侧日期选择面板 + 右侧时分秒选择面板，完全兼容 MUI 设计风格
        </Alert>
      </Box>

      <Stack spacing={3}>
        {/* 基础用法 */}
        <StyledCard>
          <CardHeader
            title="📅⏰ 基础用法"
            subheader="默认配置的日期时间选择器"
            titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
          />
          <CardContent>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <FormLabel sx={{ mb: 1, fontWeight: 500 }}>选择日期时间</FormLabel>
              <DateTimePicker
                value={value1}
                onChange={setValue1}
                placeholder="请选择日期时间"
              />
            </FormControl>

            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                选中值:
              </Typography>
              <ResultChip
                label={value1 ? value1.format('YYYY-MM-DD HH:mm:ss') : '未选择'}
                variant="outlined"
                size="small"
                color={value1 ? "primary" : "default"}
              />
            </Stack>
          </CardContent>
        </StyledCard>

        {/* 带默认值 */}
        <StyledCard>
          <CardHeader
            title="📅⏰ 带默认值"
            subheader="设置了默认值的日期时间选择器"
            titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
          />
          <CardContent>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <FormLabel sx={{ mb: 1, fontWeight: 500 }}>选择日期时间</FormLabel>
              <DateTimePicker
                value={value2}
                onChange={setValue2}
                placeholder="请选择日期时间"
              />
            </FormControl>

            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                选中值:
              </Typography>
              <ResultChip
                label={value2 ? value2.format('YYYY-MM-DD HH:mm:ss') : '未选择'}
                variant="outlined"
                size="small"
                color={value2 ? "primary" : "default"}
              />
            </Stack>
          </CardContent>
        </StyledCard>

        {/* 禁用确认模式 */}
        <StyledCard>
          <CardHeader
            title="📅⏰ 禁用确认模式"
            subheader="选择后立即生效，无需点击确认按钮"
            titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
          />
          <CardContent>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <FormLabel sx={{ mb: 1, fontWeight: 500 }}>选择日期时间</FormLabel>
              <DateTimePicker
                value={value3}
                onChange={setValue3}
                placeholder="请选择日期时间"
                needConfirm={false}
              />
            </FormControl>

            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                选中值:
              </Typography>
              <ResultChip
                label={value3 ? value3.format('YYYY-MM-DD HH:mm:ss') : '未选择'}
                variant="outlined"
                size="small"
                color={value3 ? "primary" : "default"}
              />
            </Stack>
          </CardContent>
        </StyledCard>

        {/* 操作按钮 */}
        <StyledCard>
          <CardHeader
            title="🎯 操作测试"
            subheader="测试各种操作功能"
            titleTypographyProps={{ variant: 'h6', fontWeight: 500 }}
          />
          <CardContent>
            <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
              <Button
                variant="contained"
                onClick={() => {
                  console.log('当前所有值:', {
                    value1: value1?.format('YYYY-MM-DD HH:mm:ss'),
                    value2: value2?.format('YYYY-MM-DD HH:mm:ss'),
                    value3: value3?.format('YYYY-MM-DD HH:mm:ss'),
                  });
                }}
              >
                打印所有值
              </Button>
              <Button
                variant="outlined"
                onClick={() => {
                  setValue1(undefined);
                  setValue2(dayjs());
                  setValue3(undefined);
                }}
              >
                重置所有值
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => {
                  const now = dayjs();
                  setValue1(now);
                  setValue2(now);
                  setValue3(now);
                }}
              >
                设置为当前时间
              </Button>
            </Stack>

            <Alert severity="success">
              <AlertTitle>✅ 测试通过</AlertTitle>
              <Typography variant="body2">
                DateTimePicker 组件已成功创建并可以正常使用！
              </Typography>
            </Alert>
          </CardContent>
        </StyledCard>
      </Stack>
    </Box>
  );
};

export default DateTimePickerTest;
