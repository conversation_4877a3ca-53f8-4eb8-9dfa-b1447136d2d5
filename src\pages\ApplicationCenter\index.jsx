/* eslint-disable react/prop-types */
import { Link } from "react-router-dom";
import MainCard from "@/components/MainCard";
import ApplicationCenterErrorBoundary from "@/components/ApplicationCenterErrorBoundary";
import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Box,
  Button,
} from "@mui/material";
import screenDirectLogo from "@/assets/Home/<EMAIL>";
import ZataLogo from "@/assets/Home/<EMAIL>";
import NuTagLogo from "@/assets/Home/<EMAIL>";
import fristBgPhoto from "@/assets/Images/Rectangle <EMAIL>";
import computer from "@/assets/Images/applicationCenter/pngwing.com (44)@2x.png";
import cmsContent from "@/assets/Images/applicationCenter/CMSZKDIGIMAXPNG.png";
import nutagContent from "@/assets/Images/applicationCenter/<EMAIL>";
import zataContent from "@/assets/Images/applicationCenter/zata.png";
import cmsBgContent from "@/assets/Images/applicationCenter/<EMAIL>";
import eplLabel from "@/assets/Images/applicationCenter/eplLabel.png";
import AICamera from "@/assets/Images/applicationCenter/AICamera.png";
import { pxToRem } from "@/utils/zkUtils.js";
import { useTranslation } from "react-i18next";
import actions from "@/utils/actions";
import { getToken } from "@/utils/auth";
import { useStateUserInfo } from "@/hooks/user";
import { getAuthButton } from "@/service/api/user";
import { useNavigate } from "react-router-dom";
import { fetchAndStoreAppList } from "@/utils/appListManager";

const index = () => {
  const { t } = useTranslation();
  const userInfo = useStateUserInfo();
  const [useAppList, setUseAppList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const mountedRef = useRef(true);
  const maxRetries = 3;

  // 加载应用列表的函数
  const loadAppList = useCallback(
    async (isRetry = false) => {
      if (!mountedRef.current) return;

      try {
        setLoading(true);
        setError(null);

        if (isRetry) {
          console.log(`🔄 重试加载应用列表 (${retryCount + 1}/${maxRetries})`);
        }

        const appList = await fetchAndStoreAppList();

        if (mountedRef.current) {
          setUseAppList(appList || []);
          setLoading(false);
          setRetryCount(0); // 成功后重置重试计数
          console.log("✅ 应用列表加载成功");
        }
      } catch (error) {
        console.error("获取应用列表失败:", error);

        if (mountedRef.current) {
          setError(error);
          setLoading(false);

          // 如果重试次数未达到上限，自动重试
          if (retryCount < maxRetries) {
            const newRetryCount = retryCount + 1;
            setRetryCount(newRetryCount);

            setTimeout(() => {
              if (mountedRef.current) {
                loadAppList(true);
              }
            }, 2000 * newRetryCount); // 递增延迟
          }
        }
      }
    },
    [retryCount, maxRetries]
  );

  // 手动重试函数
  const handleManualRetry = useCallback(() => {
    setRetryCount(0);
    loadAppList(true);
  }, [loadAppList]);

  // 监听微前端清理和刷新事件
  useEffect(() => {
    const handleApplicationCenterRefresh = (event) => {
      console.log("🔄 收到应用中心刷新事件，重新加载数据:", event.detail);
      loadAppList(false);
    };

    const handleMicroAppCleanup = () => {
      console.log("🧹 检测到微前端清理，检查是否需要重新加载数据");
      // 延迟一点时间确保清理完成
      setTimeout(() => {
        if (mountedRef.current && (error || useAppList.length === 0)) {
          loadAppList(false);
        }
      }, 500);
    };

    window.addEventListener(
      "application-center-refresh",
      handleApplicationCenterRefresh
    );
    window.addEventListener("microapp-cleanup", handleMicroAppCleanup);

    return () => {
      window.removeEventListener(
        "application-center-refresh",
        handleApplicationCenterRefresh
      );
      window.removeEventListener("microapp-cleanup", handleMicroAppCleanup);
    };
  }, [loadAppList, error, useAppList.length]);

  // 初始加载
  useEffect(() => {
    loadAppList(false);

    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 如果正在加载，显示加载状态
  if (loading && useAppList.length === 0) {
    return (
      <ApplicationCenterErrorBoundary>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: 400,
          }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            {t("appCenter.loading", "正在加载应用中心...")}
          </Typography>
        </Box>
      </ApplicationCenterErrorBoundary>
    );
  }

  // 如果有错误且重试次数已达上限，显示错误状态
  if (error && retryCount >= maxRetries) {
    return (
      <ApplicationCenterErrorBoundary>
        <Alert
          severity="error"
          action={
            <Button onClick={handleManualRetry}>
              {t("common.retry", "重试")}
            </Button>
          }>
          {t("appCenter.loadError", "应用中心加载失败，请重试")}
        </Alert>
      </ApplicationCenterErrorBoundary>
    );
  }

  return (
    <ApplicationCenterErrorBoundary>
      <React.Fragment>
        <MainCard style={{ marginBottom: "10px" }}>
          <Typography variant="h4">{t("appCenter.title")}</Typography>
        </MainCard>

        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
          }}>
          <MyApplicationCard
            applicationLogo={screenDirectLogo}
            applicationCode={"SD"}
            text={"Screen Direct"}
            onClick={async () => {
              try {
                // 1. 确保 qiankun 已启动
                if (window.__QIANKUN_START__) {
                  window.__QIANKUN_START__();
                }

                // 2. 获取权限信息
                const res = await getAuthButton("SD");
                const combinedPermissions = [
                  ...(userInfo?.permissions || []),
                  ...(res?.data || []),
                ];

                const updatedUserInfo = {
                  ...userInfo,
                  permissions: combinedPermissions,
                };

                // 3. 设置全局状态
                actions.setGlobalState({
                  token: getToken(),
                  userInfo: updatedUserInfo,
                });

                console.log("✅ CMS应用权限设置完成，准备跳转");
              } catch (error) {
                console.error("❌ CMS应用权限设置失败:", error);
              }
            }}
            bgImage={fristBgPhoto}
            disabled={
              useAppList?.find((app) => app.code == "SD")?.isSubscribed == "N"
            }
            lastImage={cmsContent}
            pathRouter={"/cms-app"}
            linkButtonText={
              useAppList?.find((app) => app.code == "SD")?.isSubscribed == "N"
                ? t("appCenter.subscribe")
                : t("appCenter.open")
            }
            background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
            <Grid
              sx={{
                padding: "0px 80px 20px 40px",
                font: `normal normal bold 18px/26px Proxima Nova`,
                color: "#222222",
              }}>
              {t("appCenter.cms_content")}
            </Grid>
            <img
              src={cmsBgContent}
              alt="无法加载"
              srcSet=""
              style={{
                zIndex: "11111111",
                width: "75%",
                height: "100%",
                marginLeft: "13%",
                marginTop: "5%",
                border: "10px solid #000",
              }}
            />
          </MyApplicationCard>

          <MyApplicationCard
            applicationLogo={NuTagLogo}
            applicationCode={"NT"}
            text={"NuTag"}
            disabled={
              useAppList?.find((app) => app.code == "NT")?.isSubscribed == "N"
            }
            onClick={async () => {
              try {
                // 1. 确保 qiankun 已启动
                if (window.__QIANKUN_START__) {
                  window.__QIANKUN_START__();
                }

                // 2. 获取权限信息
                const res = await getAuthButton("NT");
                const combinedPermissions = [
                  ...(userInfo?.permissions || []),
                  ...(res?.data || []),
                ];

                const updatedUserInfo = {
                  ...userInfo,
                  permissions: combinedPermissions,
                };

                // 3. 设置全局状态
                actions.setGlobalState({
                  token: getToken(),
                  userInfo: updatedUserInfo,
                });

                console.log("✅ NuTag应用权限设置完成，准备跳转");
              } catch (error) {
                console.error("❌ NuTag应用权限设置失败:", error);
              }
            }}
            bgImage={fristBgPhoto}
            lastImage={nutagContent}
            pathRouter={"/e-price-tag-app"}
            linkButtonText={
              useAppList?.find((app) => app.code == "NT")?.isSubscribed == "N"
                ? t("appCenter.subscribe")
                : t("appCenter.open")
            }
            background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
            <Grid
              sx={{
                padding: "0px 80px 20px 40px",
                font: `normal normal bold 18px/26px Proxima Nova`,
                color: "#222222",
              }}>
              {t("appCenter.nutag_content")}
            </Grid>

            <Grid
              sx={{
                display: "flex",
                justifyContent: "center",
              }}>
              <img
                src={eplLabel}
                alt="无法加载"
                srcSet=""
                style={{
                  width: "80%",

                  marginLeft: "5%",
                  marginTop: "5%",
                }}
              />
            </Grid>
          </MyApplicationCard>

          <MyApplicationCard
            disabled={
              useAppList?.find((app) => app.code == "ZT")?.isSubscribed == "N"
            }
            applicationLogo={ZataLogo}
            applicationCode={"ZT"}
            text={"Zata"}
            onClick={async () => {
              try {
                // 1. 确保 qiankun 已启动
                if (window.__QIANKUN_START__) {
                  window.__QIANKUN_START__();
                }

                // 2. 获取权限信息
                const res = await getAuthButton("ZT");
                const combinedPermissions = [
                  ...(userInfo?.permissions || []),
                  ...(res?.data || []),
                ];

                const updatedUserInfo = {
                  ...userInfo,
                  permissions: combinedPermissions,
                };

                // 3. 设置全局状态
                actions.setGlobalState({
                  token: getToken(),
                  userInfo: updatedUserInfo,
                });

                console.log("✅ Zata应用权限设置完成，准备跳转");
              } catch (error) {
                console.error("❌ Zata应用权限设置失败:", error);
              }
            }}
            bgImage={fristBgPhoto}
            lastImage={zataContent}
            pathRouter={"/retail-ai-app"}
            linkButtonText={
              useAppList?.find((app) => app.code == "ZT")?.isSubscribed == "N"
                ? t("appCenter.subscribe")
                : t("appCenter.open")
            }
            background={`transparent linear-gradient(180deg, #FFFFFF 0%, #F3F3F333 78%, #F0F0F000 100%) 0% 0% no-repeat padding-box`}>
            <Grid
              sx={{
                padding: "0px 80px 20px 40px",
                font: `normal normal bold 18px/26px Proxima Nova`,
                color: "#222222",
              }}>
              {t("appCenter.zata_content")}
            </Grid>

            <Grid
              sx={{
                position: "relative",
                display: "flex",
                justifyContent: "center",
              }}>
              <img
                src={AICamera}
                alt="无法加载"
                style={{
                  zIndex: "1",
                  left: "35px",
                  top: "14px",
                  position: "relative",
                }}
              />
              <div
                style={{
                  position: "absolute",
                  left: "50%",
                  marginLeft: "-130px",
                  top: "80px",
                  // left:'260px',
                  // transform: 'translate(-50%, -50%)',
                  width: "0",
                  height: "0",
                  borderLeft: "130px solid transparent",
                  borderRight: "130px solid transparent",
                  borderBottom: "240px solid #78BC2757",
                  background:
                    "linear-gradient(180deg, #78BC2757 0%, #78BC2700 100%)",
                  zIndex: "2",
                }}></div>
            </Grid>
          </MyApplicationCard>
        </Grid>
      </React.Fragment>
    </ApplicationCenterErrorBoundary>
  );
};
export default index;

const MyApplicationCard = (props) => {
  const userInfo = useStateUserInfo();
  const navigate = useNavigate();
  const {
    applicationLogo,
    applicationCode,
    text,
    children,
    lastImage,
    pathRouter,
    linkButtonText,
    background,
    onClick = () => {},
    disabled = false,
  } = props;
  return (
    <Grid
      item
      sx={{
        position: "relative",
      }}>
      <Grid
        sx={{
          position: "relative",
          minHeight: "680px",
          // boxShadow: "0px 0px 10px #0000000D",
          // border: "1px solid #E0E0E0",
          // backgroundColor: "#ffffff",
          borderRadius: "10px",
          maxWidth: pxToRem(490),
          width: "100%",
          background: background,
          opacity: 1,
          boxSizing: "border-box",
        }}>
        <Grid
          sx={{
            padding: "20px 20px 20px 40px",
            marginTop: "20px",
            justifyContent: "space-between",
          }}
          container
          display="flex"
          position="relative"
          zIndex={2}>
          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}>
            <Grid item>
              <img
                src={applicationLogo}
                alt="加载失败"
                style={{
                  width: pxToRem(81),
                  height: pxToRem(81),
                }}
              />
            </Grid>

            <Grid item m={2} width={"50px"}>
              <Typography variant="h4">{text}</Typography>
            </Grid>
          </Grid>

          <Grid
            sx={{
              display: "flex",
              alignItems: "center",
            }}
            item>
            {disabled ? (
              <Button
                disabled={true}
                style={{
                  border: "2px solid #1487CA",
                  borderRadius: "10px",
                  height: "50px",
                  minWidth: "100px",
                  opacity: 1,
                  textDecorationLine: "none",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#1487CA",
                  fontWeight: "600",
                  boxShadow: `0px 3px 6px #00000029`,
                  cursor: "pointer",
                  backgroundColor: "rgba(20, 135, 202, 0.1)",
                  transition: "all 0.3s ease",
                }}>
                {linkButtonText}
              </Button>
            ) : (
              <Link
                to={pathRouter}
                onClick={async (e) => {
                  // 执行权限设置等操作
                  await onClick();

                  // 给一点时间让 qiankun 启动和容器准备
                  setTimeout(() => {
                    console.log(`🚀 准备跳转到: ${pathRouter}`);
                  }, 100);
                }}
                style={{
                  border: "1px solid #78BC27",
                  borderRadius: "10px",
                  height: "50px",
                  minWidth: "100px",
                  opacity: 1,
                  textDecorationLine: "none",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#78BC27",
                  fontWeight: "600",
                  boxShadow: `0px 3px 6px #00000029`,
                  cursor: "pointer",
                  backgroundColor: "rgba(120, 188, 39, 0.1)",
                  transition: "all 0.3s ease",
                }}>
                {linkButtonText}
              </Link>
            )}
          </Grid>
        </Grid>

        <Grid>{children}</Grid>

        <Grid
          sx={{
            position: "absolute",
            bottom: "-70px",
            width: "100%",
            height: "185px",
            display: "flex",
            justifyContent: "center",
          }}>
          <Grid
            sx={{
              position: "relative",
              width: "100%",
              height: "185px",
              maxWidth: "329px",
            }}>
            {/* 笔记本电脑图片 */}
            <img
              style={{
                width: "90%", // 笔记本图片占满父容器
                height: "90%", // 笔记本图片占满父容器
                zIndex: 1, // 确保背景图片在底层
                position: "absolute", // 笔记本电脑图片的定位
                top: 0,
                left: 0,
              }}
              src={computer}
              alt="加载失败"
            />

            {/* 显示在屏幕上的图片 */}
            <img
              style={{
                width: "64%", // 根据屏幕比例调整大小
                height: "58%", // 根据屏幕比例调整大小
                zIndex: 2, // 确保屏幕图片在笔记本电脑之上
                position: "absolute", // 屏幕图片绝对定位
                top: "12%", // 调整位置，使其居中显示在屏幕上
                left: "13%", // 调整位置，使其居中显示在屏幕上
                borderRadius: "3px",
                transform: "perspective(1000px) rotateX(1deg) rotateY(-2deg)", // 3D 旋转效果
              }}
              src={lastImage}
              alt="加载失败"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};
