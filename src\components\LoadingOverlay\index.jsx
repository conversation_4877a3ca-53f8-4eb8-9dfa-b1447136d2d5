import React, { useState, useEffect } from "react";
import { Box, Typography, LinearProgress, Fade } from "@mui/material";
import { styled, keyframes } from "@mui/material/styles";

// 创建动画
const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

const float = keyframes`
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
`;

// 样式化组件
const OverlayContainer = styled(Box)(({ theme }) => ({
  position: "fixed",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  backgroundColor: "rgba(255, 255, 255, 0.95)",
  backdropFilter: "blur(8px)",
  zIndex: 9999,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  fontFamily: theme.typography.fontFamily,
}));

const LoadingContent = styled(Box)({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  maxWidth: "400px",
  padding: "2rem",
  textAlign: "center",
});

const LogoContainer = styled(Box)({
  marginBottom: "2rem",
  animation: `${pulse} 2s ease-in-out infinite`,
});

const LoadingIcon = styled(Box)({
  width: "60px",
  height: "60px",
  borderRadius: "50%",
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  animation: `${float} 3s ease-in-out infinite`,
  marginBottom: "1rem",
});

const LoadingSpinner = styled(Box)({
  width: "30px",
  height: "30px",
  border: "3px solid rgba(255, 255, 255, 0.3)",
  borderTop: "3px solid white",
  borderRadius: "50%",
  animation: "spin 1s linear infinite",
  "@keyframes spin": {
    "0%": { transform: "rotate(0deg)" },
    "100%": { transform: "rotate(360deg)" },
  },
});

const ProgressContainer = styled(Box)({
  width: "100%",
  marginTop: "1.5rem",
});

const LoadingOverlay = ({
  visible = false,
  message = "正在加载...",
  progress = null,
  showProgress = false,
  type = "default", // default, transition, refresh, page-refresh
  onComplete = null,
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  // 根据类型调整样式和行为
  const getTypeConfig = () => {
    switch (type) {
      case "page-refresh":
        return {
          backgroundColor: "rgba(255, 255, 255, 0.98)",
          backdropFilter: "blur(12px)",
          zIndex: 10000, // 更高的层级确保覆盖所有内容
          message: message || "正在刷新页面...",
          showSpinner: true,
          progressColor: "#1976d2",
        };
      case "refresh":
        return {
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(8px)",
          zIndex: 9999,
          message: message || "正在刷新...",
          showSpinner: true,
          progressColor: "#4caf50",
        };
      case "transition":
        return {
          backgroundColor: "rgba(255, 255, 255, 0.9)",
          backdropFilter: "blur(6px)",
          zIndex: 9999,
          message: message || "正在切换...",
          showSpinner: true,
          progressColor: "#ff9800",
        };
      default:
        return {
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(8px)",
          zIndex: 9999,
          message: message || "正在加载...",
          showSpinner: true,
          progressColor: "#2196f3",
        };
    }
  };

  const typeConfig = getTypeConfig();

  // 模拟进度条动画
  useEffect(() => {
    if (visible && showProgress) {
      const interval = setInterval(() => {
        setDisplayProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90;
          }
          return prev + Math.random() * 15;
        });
      }, 200);

      return () => clearInterval(interval);
    } else {
      setDisplayProgress(0);
    }
  }, [visible, showProgress]);

  // 完成进度条
  useEffect(() => {
    if (progress === 100) {
      setDisplayProgress(100);

      // 页面刷新类型时延长显示时间，确保覆盖整个刷新过程
      const isPageRefresh = type === "page-refresh" || type === "refresh";
      const delay = isPageRefresh ? 1000 : 500; // 页面刷新时延长显示时间

      setTimeout(() => {
        onComplete && onComplete();
      }, delay);
    }
  }, [progress, onComplete, type]);

  // 更新消息
  useEffect(() => {
    setCurrentMessage(message);
  }, [message]);

  if (!visible) return null;

  return (
    <Fade in={visible} timeout={300}>
      <Box
        sx={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          backgroundColor: typeConfig.backgroundColor,
          backdropFilter: typeConfig.backdropFilter,
          zIndex: typeConfig.zIndex,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}>
        <LoadingContent>
          <LogoContainer>
            <LoadingIcon>
              <LoadingSpinner />
            </LoadingIcon>
          </LogoContainer>

          <Typography
            variant="h6"
            sx={{
              color: "#333",
              fontWeight: 500,
              marginBottom: "0.5rem",
            }}>
            {currentMessage}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              color: "#666",
              marginBottom: showProgress ? "1rem" : 0,
            }}>
            请稍候，正在为您准备最佳体验
          </Typography>

          {showProgress && (
            <ProgressContainer>
              <LinearProgress
                variant="determinate"
                value={progress !== null ? progress : displayProgress}
                sx={{
                  height: "6px",
                  borderRadius: "3px",
                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                  "& .MuiLinearProgress-bar": {
                    borderRadius: "3px",
                    backgroundColor: typeConfig.progressColor,
                  },
                }}
              />
              <Typography
                variant="caption"
                sx={{
                  color: "#888",
                  marginTop: "0.5rem",
                  display: "block",
                }}>
                {Math.round(progress !== null ? progress : displayProgress)}%
              </Typography>
            </ProgressContainer>
          )}
        </LoadingContent>
      </Box>
    </Fade>
  );
};

export default LoadingOverlay;
