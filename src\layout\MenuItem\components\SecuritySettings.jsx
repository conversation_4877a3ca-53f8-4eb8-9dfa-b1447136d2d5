import React from "react";
import CustInput from "@c/CustInput";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
import RegexCode from "@/enums/RegexCode";
import { Grid, Button } from "@mui/material";
function SecuritySettings() {
  // 国际化
  const { t } = useTranslation();
  const areaFormik = useFormik({
    initialValues: {
      oldpassword: "",
      newpassword: "",
      confirmpassword: "",
    },
    onSubmit: (values) => {},

    validationSchema: Yup.object().shape({
      oldpassword: Yup.string().required(t("common.common_area_name_not_null")),
      newpassword: Yup.string()
        .min(12, t("密码长度至少12位"))
        .max(64, t("密码长度不能超过64位"))
        .matches(RegexCode.PASSWORD_REGEX, {
          message: t(
            "密码必须包含大写字母、小写字母、数字、特殊字符，不能包含空格或表情符号"
          ),
          excludeEmptyString: true,
        })
        .required(t("新密码是必传的")),
      confirmpassword: Yup.string()
        .oneOf([Yup.ref("newpassword"), null], t("确认密码必须与新密码一致"))
        .required(t("确认密码是必传的")),
    }),
  });

  return (
    <React.Fragment>
      <form noValidate onSubmit={areaFormik.handleSubmit}>
        <Grid xs={12} md={6} lg={12} sm={4}>
          <Grid>
            <CustInput
              name={"oldpassword"}
              label={"Old Password*"}
              required={true}
              type={"password"}
              formik={areaFormik}></CustInput>
          </Grid>

          <Grid mt={2}>
            <CustInput
              name={"newpassword"}
              label={"New Password"}
              required={true}
              type={"password"}
              inputProps={{
                maxLength: 64, // 限制最大长度为64位
              }}
              formik={areaFormik}></CustInput>
          </Grid>

          <Grid mt={2}>
            <CustInput
              name={"confirmpassword"}
              label={"Confirm Password"}
              required={true}
              type={"password"}
              inputProps={{
                maxLength: 64, // 限制最大长度为64位
              }}
              formik={areaFormik}></CustInput>
          </Grid>

          <Button
            id="AddOutlet-button-01"
            variant="contained"
            size="medium"
            className="text-transform-none"
            type="submit"
            style={{
              borderRadius: "15px",
              opacity: 1,
              padding: "12px 16px",
              width: "248px",
              height: "65px",
              position: "absolute",
              right: "40px",
              marginTop: "20px",
              font: "normal normal bold 16px/20px Proxima Nova",
              color: "#FFFFFF",
              background:
                "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
            }}>
            Save Changes
          </Button>
        </Grid>
      </form>
    </React.Fragment>
  );
}

export default SecuritySettings;
