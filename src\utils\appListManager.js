/**
 * 应用列表管理工具
 * 用于获取和管理用户的应用列表数据
 */

import { getAppList } from "@/service/api/application";
import { setUserAppList } from "@/store/reducers/user";
import { store } from "@/store";

/**
 * 获取应用列表并存储到Redux
 * @returns {Promise<Array>} 应用列表
 */
export const fetchAndStoreAppList = async () => {
  try {
    const response = await getAppList();

    if (response?.code === "00000000") {
      const appList = response.data || [];

      // 存储到Redux
      store.dispatch(setUserAppList(appList));

      return appList;
    } else {
      // 存储空数组到Redux
      store.dispatch(setUserAppList([]));

      return [];
    }
  } catch (error) {
    // 存储空数组到Redux
    store.dispatch(setUserAppList([]));

    return [];
  }
};

/**
 * 从Redux获取应用列表
 * @returns {Array} 应用列表
 */
export const getAppListFromStore = () => {
  const state = store.getState();
  const appList = state.user?.userInfor?.useAppList || [];

  return appList;
};

/**
 * 检查应用列表是否已加载
 * @returns {boolean} 是否已加载
 */
export const isAppListLoaded = () => {
  const state = store.getState();
  const appList = state.user?.userInfor?.useAppList;

  return Array.isArray(appList) && appList.length > 0;
};

/**
 * 确保应用列表已加载
 * 如果未加载则自动获取
 * @returns {Promise<Array>} 应用列表
 */
export const ensureAppListLoaded = async () => {
  if (isAppListLoaded()) {
    // console.log("📋 应用列表已存在，直接使用");
    return getAppListFromStore();
  } else {
    // console.log("📋 应用列表未加载，开始获取...");
    return await fetchAndStoreAppList();
  }
};

/**
 * 刷新应用列表
 * 强制重新获取应用列表数据
 * @returns {Promise<Array>} 应用列表
 */
export const refreshAppList = async () => {
  // console.log("🔄 强制刷新应用列表...");
  return await fetchAndStoreAppList();
};

/**
 * 清空应用列表
 */
export const clearAppList = () => {
  // console.log("🗑️ 清空应用列表");
  store.dispatch(setUserAppList([]));
};

/**
 * 监听应用列表变化
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export const subscribeToAppList = (callback) => {
  let currentAppList = getAppListFromStore();

  const unsubscribe = store.subscribe(() => {
    const newAppList = getAppListFromStore();

    // 检查应用列表是否发生变化
    if (JSON.stringify(currentAppList) !== JSON.stringify(newAppList)) {
      currentAppList = newAppList;
      callback(newAppList);
    }
  });

  return unsubscribe;
};

// 开发环境下暴露调试方法
if (import.meta.env.DEV && typeof window !== "undefined") {
  window.__appListManager = {
    fetch: fetchAndStoreAppList,
    get: getAppListFromStore,
    isLoaded: isAppListLoaded,
    ensure: ensureAppListLoaded,
    refresh: refreshAppList,
    clear: clearAppList,


  };

}

export default {
  fetchAndStoreAppList,
  getAppListFromStore,
  isAppListLoaded,
  ensureAppListLoaded,
  refreshAppList,
  clearAppList,
  subscribeToAppList,
};
