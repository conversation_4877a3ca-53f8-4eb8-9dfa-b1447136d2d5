import bizRoutes from "./menus/index";
import { isMicroAppPath } from "@/components/MicroAppContainer";

const routes = [
  {
    path: "/login",
    component: () => import("@/pages/Login/index"),
    meta: {
      title: "登录页",
      i18n: "media_login",
      needLogin: false,
      id: "000995489808121577346",
    },
  },
  {
    path: "/",
    redirect: "/login",
    id: "516489808121577346",
  },
  // 异常页面路由 - 不使用MainLayout，直接渲染
  {
    path: "/404",
    component: () => import("@/router/ExceptionComponent/NotFound"),
    meta: {
      title: "页面未找到",
      i18n: "page_not_found",
      needLogin: false,
      hideLayout: true, // 标记隐藏布局
    },
  },
  {
    path: "/403",
    component: () => import("@/router/ExceptionComponent/PermissionError"),
    meta: {
      title: "访问被拒绝",
      i18n: "permission_denied",
      needLogin: false,
      hideLayout: true,
    },
  },
  {
    path: "/500",
    component: () => import("@/router/ExceptionComponent/ServerError"),
    meta: {
      title: "服务器错误",
      i18n: "server_error",
      needLogin: false,
      hideLayout: true,
    },
  },
  {
    path: "/subscription-required",
    component: () => import("@/pages/SubscriptionRequired"),
    meta: {
      title: "需要订阅",
      i18n: "subscription_required",
      needLogin: true,
      hideLayout: true,
    },
  },

  ...bizRoutes,
  // 微前端子应用路由 - 必须放在通配符404之前
  {
    path: "/cms-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "cms-app", title: "CMS应用" },
    meta: {
      title: "CMS应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
  {
    path: "/retail-ai-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "retail-ai-app", title: "零售AI应用" },
    meta: {
      title: "零售AI应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
  {
    path: "/e-price-tag-app/*",
    component: () => import("@/components/MicroAppContainer"),
    props: { appName: "e-price-tag-app", title: "电子价签应用" },
    meta: {
      title: "电子价签应用",
      needLogin: true,
      isMicroApp: true,
    },
  },
  // 通配符404页面 - 必须放在最后，但需要排除微前端路由
  {
    path: "*",
    component: () => import("@/router/ExceptionComponent/NotFound"),
    meta: {
      title: "页面未找到",
      i18n: "page_not_found",
      needLogin: false,
      hideLayout: true,
    },
    // 添加路由守卫，排除微前端路径
    beforeEnter: (to) => {
      if (isMicroAppPath(to.path)) {
        return false; // 阻止路由跳转到404
      }
    },
  },
];

export default routes;
