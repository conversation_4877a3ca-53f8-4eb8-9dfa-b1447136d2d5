import React, { useState, useMemo, useEffect } from "react";
import ZKDialog from "@/components/ZKDialog.jsx";
import { useTranslation } from "react-i18next";
import CustomInput from "@c/CustInput";
import SummitButton from "@c/SubmitButton";
import ZkTecoTable from "@c/ZktecoTable/index";
import { getPrincipaList } from "@s/api/principal";
import { Grid, Avatar, Radio } from "@mui/material";
import ZkTooltip from "@/components/ZkTooltip";
function Select(props) {
  const { t } = useTranslation();
  const { open, setOpen, setSelectedId, selectedId, formik } = props;

  // 搜索输入框绑定
  const [serchName, setSeachName] = useState("");
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [isRefetching, setIsRefetching] = useState(false);
  const [rowCount, setRowCount] = useState(0);

  // 分页
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  // 表格列定义（依赖 selectedId，修复单选）
  const columns = useMemo(
    () => [
      {
        header: t("Select"),
        enableColumnActions: false,
        enableClickToCopy: false,
        enableSorting: false,
        disableTooltip: true, // 禁用tooltip提示
        Cell: ({ row }) => (
          <Radio
            checked={selectedId?.id === row.original.id}
            onChange={() => setSelectedId(row.original)}
          />
        ),
      },
      {
        accessorKey: "logo",
        header: t("common.common_company_logo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Avatar
            src={row.original.photo}
            alt={"Logo"}
            size="medium"
            sx={{
              width: "121px",
              height: "50px",
              borderRadius: "8px",
              border: "1px solid #E3E3E3",
            }}
          />
        ),
      },
      {
        accessorKey: "name",
        header: t("principal.principal_name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "email",
        header: t("subscription.emailAddress"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "phone",
        header: t("principal.mobile_number"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
    ],
    [selectedId]
  );

  // 构建参数
  const buildParams = (extraParams = {}) => ({
    page: pagination.pageIndex + 1,
    pageSize: pagination.pageSize,
    type: "2",
    name: serchName || "",
    ...extraParams,
  });

  // 获取表格数据
  const getTableData = (params = {}) => {
    const finalParams = buildParams(params);
    if (!data.length && !params.page) setIsLoading(true);
    else setIsRefetching(true);

    getPrincipaList(finalParams)
      .then((res) => {
        setData(res.data.data);
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch(() => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  // 分页、每页数量变化时自动加载
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  // 查询按钮
  const handlerSeacher = () => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 })); // 重置页码
    getTableData({ pageIndex: 1 }); // 重新请求
  };

  // 重置按钮
  const handlerReset = () => {
    setSeachName("");
    setPagination({ pageIndex: 0, pageSize: 5 }); // 重置页码与页大小
    getTableData({
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      type: "2",
      name: "",
    }); // 重置请求
  };

  // 回车键触发查询
  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      handlerSeacher();
    }
  };

  return (
    <React.Fragment>
      <ZKDialog
        width={"1000px"}
        title={t("Select a Principal")}
        open={open}
        setOpen={setOpen}
        handlerSubmit={() => {
          setOpen(false);
          formik.setFieldValue("contactEmail", selectedId.email);
          formik.setFieldValue("departmentId", selectedId.id);
        }}>
        <Grid container>
          {/* 查询区域 */}
          <Grid container alignItems="center">
            <Grid item xs={4}>
              <CustomInput
                placeholder={t("principal.principal_name")}
                value={serchName}
                handleChange={(e) => setSeachName(e.target.value)}
                onKeyDown={handleKeyDown} // 绑定回车
              />
            </Grid>
            <Grid item xs={6} ml={2}>
              <SummitButton
                affirmText={t("common.common_query")}
                cancelText={t("common.common_reset")}
                handlerSubmit={handlerSeacher}
                CancelSubmit={handlerReset}
              />
            </Grid>
          </Grid>

          {/* 表格区域 */}
          <Grid container mt={2}>
            <ZkTecoTable
              showTopBar={false}
              isShowTop={false}
              data={data}
              columns={columns}
              showAdd={false}
              enableRowActions={false}
              rowCount={rowCount}
              getRowId={(originalRow) => originalRow.id} // 使用 id 作为唯一标识
              state={{
                isLoading,
                rowSelection: true,
                showProgressBars: isRefetching,
                showAlertBanner: isError,
              }}
              totalRecords={rowCount}
              rowsPerPage={pagination.pageSize}
              currentPage={pagination.pageIndex}
              onPageChange={(pageIndex) =>
                setPagination((prev) => ({ ...prev, pageIndex }))
              }
              onPageSizeChange={(pageSize) =>
                setPagination({ pageIndex: 0, pageSize })
              }
            />
          </Grid>
        </Grid>
      </ZKDialog>
    </React.Fragment>
  );
}

export default Select;
