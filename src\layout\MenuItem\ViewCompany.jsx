import React from "react";
import { useFormik } from "formik";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { useNavigate } from "react-router-dom";
import { useStateUserInfo } from "@/hooks/user.js";
import { createValidation } from "@c/Config/validationUtils.js";
function ViewCompany(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);
  const resData = useStateUserInfo();
  let formConfig = [
    // {
    //   name: "name",
    //   label: t("outlets.organization_name"),
    //   type: "input",
    //   required: true,
    //   validation: [
    //     {
    //       type: "string",
    //       message: "",
    //     },
    //     {
    //       type: "required",
    //       message: t("principal_user.required_org_name"),
    //     },
    //   ],
    // },

    {
      name: "firstName",
      label: t("branch_user.firstName"),
      type: "input",
      required: true,
      disabled: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch_user.enter_firstName"),
        },
      ],
    },
    {
      name: "lastName",
      label: t("branch_user.lastName"),
      type: "input",
      required: true,
      disabled: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch_user.enter_lastName"),
        },
      ],
    },
    // {
    //   name: "areaName",
    //   label: t("outlets.region"),
    //   type: "input",
    //   required: true,
    //   validation: [
    //     {
    //       type: "string",
    //       message: "",
    //     },
    //     {
    //       type: "required",
    //       message: t("principal_user.required_region"),
    //     },
    //   ],
    // },

    {
      name: "email",
      label: t("outlets.organization_owner_email"),
      type: "input",
      required: true,
      disabled: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal_user.required_email"),
        },
      ],
    },
    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("outlets.organization_owner_mobile"),
      type: "mobile",
      disabled: true,
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal_user.mobile_required"),
        },
      ],
    },

    // {
    //   name: "address",
    //   label: t("branch_user.address"),
    //   type: "address",
    //   placeholder: t("outlets.enter_address"),
    //   required: true,
    //   validation: [
    //     {
    //       type: "string",
    //       message: "",
    //     },
    //     {
    //       type: "required",
    //       message: t("outlets.address_required"),
    //     },
    //   ],
    // },
  ];

  // 添加表单
  const formik = useFormik({
    initialValues: {
      firstName: resData?.firstName,
      lastName: resData?.lastName,
      email: resData?.email,
      countryCode: resData?.countryCode,
      phone: resData?.phone,
    },
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        setLoading(true);
        setLoading(false);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setLoading(false);
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("common.common_org_details")}
        handleSubmit={formik.handleSubmit}
        isShowSave={false}
        handleCancle={() => {
          navigate(-1);
        }}
        callback={t("common.common_op_return")}
        loading={loading}>
        <div className="px-10 py-6">
          <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
        </div>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewCompany;
