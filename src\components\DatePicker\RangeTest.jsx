import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  FormLabel,
  Alert,
  AlertTitle,
  Grid
} from '@mui/material';
import DatePicker from './index';
import dayjs from 'dayjs';

const RangeTest = () => {
  const [rangeValue, setRangeValue] = useState([]);
  const [monthValue, setMonthValue] = useState();
  const [yearValue, setYearValue] = useState();

  const handleRangeChange = (value, dateStrings) => {
    console.log('RangePicker onChange:', { value, dateStrings });
    setRangeValue(value);
  };

  const handleMonthChange = (value, dateString) => {
    console.log('Month onChange:', { value, dateString });
    setMonthValue(value);
  };

  const handleYearChange = (value, dateString) => {
    console.log('Year onChange:', { value, dateString });
    setYearValue(value);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        日期范围和年份选择测试
      </Typography>

      <Alert severity="info" sx={{ mb: 4 }}>
        <AlertTitle>测试说明</AlertTitle>
        <Typography variant="body2">
          • 🎯 <strong>重点测试年份切换:</strong> 点击年份标题，选择不同年份，检查日历是否正确更新<br/>
          • 测试月份切换是否正常工作<br/>
          • 测试日期范围选择是否能正常点击日期<br/>
          • 请打开浏览器控制台查看详细的事件日志
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* 日期范围选择 */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅📅 日期范围选择测试
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择日期范围</FormLabel>
              <DatePicker.RangePicker
                value={rangeValue}
                onChange={handleRangeChange}
                placeholder={["开始日期", "结束日期"]}
                allowClear
                format="YYYY-MM-DD"
              />
            </FormControl>

            <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>当前范围:</strong> {rangeValue && rangeValue[0] && rangeValue[1]
                  ? `${rangeValue[0].format('YYYY-MM-DD')} ~ ${rangeValue[1].format('YYYY-MM-DD')}`
                  : '未选择'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>开始日期:</strong> {rangeValue && rangeValue[0] ? rangeValue[0].format('YYYY-MM-DD') : '未选择'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>结束日期:</strong> {rangeValue && rangeValue[1] ? rangeValue[1].format('YYYY-MM-DD') : '未选择'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>选择状态:</strong> {
                  !rangeValue || (!rangeValue[0] && !rangeValue[1]) ? '未开始选择' :
                  rangeValue[0] && !rangeValue[1] ? '已选择开始日期，等待选择结束日期' :
                  rangeValue[0] && rangeValue[1] ? '范围选择完成' : '未知状态'
                }
              </Typography>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>🔍 年份切换专项测试步骤:</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary" component="div">
                1. 点击输入框打开日期范围选择器<br/>
                2. <strong>当前显示:</strong> 2025年5月<br/>
                3. <strong>目标测试:</strong> 点击年份标题 "2025年5月"<br/>
                4. <strong>选择年份:</strong> 在年份列表中选择 "2024"<br/>
                5. <strong>期望结果:</strong> 左上角应该显示 "2024年5月"（不是 "2025年5月"）<br/>
                6. <strong>再次测试:</strong> 点击月份，选择 "3月"<br/>
                7. <strong>期望结果:</strong> 左上角应该显示 "2024年3月"<br/>
                8. <strong>查看控制台:</strong> 应该有详细的调试日志
              </Typography>
            </Box>

            <Box sx={{ mt: 2, p: 2, backgroundColor: 'warning.light', borderRadius: 1 }}>
              <Typography variant="body2" color="text.primary">
                <strong>⚠️ 已知问题:</strong> 年份切换后可能显示错误的年份（如 2025年3月 而不是 2024年3月）
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* 年份选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅 年份选择测试
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择年份</FormLabel>
              <DatePicker
                value={yearValue}
                onChange={handleYearChange}
                placeholder="请选择年份"
                picker="year"
                format="YYYY"
                allowClear
              />
            </FormControl>

            <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>当前年份:</strong> {yearValue ? yearValue.format('YYYY') : '未选择'}
              </Typography>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>测试重点:</strong> 检查年份列表是否被遮挡，是否可以滚动选择
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* 月份选择 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              📅 月份选择测试
            </Typography>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1 }}>选择月份</FormLabel>
              <DatePicker
                value={monthValue}
                onChange={handleMonthChange}
                placeholder="请选择月份"
                picker="month"
                format="YYYY-MM"
                allowClear
              />
            </FormControl>

            <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>当前月份:</strong> {monthValue ? monthValue.format('YYYY-MM') : '未选择'}
              </Typography>
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>测试重点:</strong> 检查月份选择是否正常工作
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RangeTest;
