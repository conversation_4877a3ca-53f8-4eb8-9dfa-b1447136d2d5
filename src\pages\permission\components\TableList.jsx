import React, { useEffect, useState } from "react";
import { getPageRoleList } from "@s/api/premission";
import { deteleRoles } from "@s/api/premission";
import ZktecoTable from "@/components/ZktecoTable/index";
import { useNavigate } from "react-router-dom";
import LayoutList from "@/layout/components/LayoutList.jsx";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import AppTap from "@/components/AppTap.jsx";
import useDict from "@/hooks/useDict.js";
import DictTag from "@c/DictTag";
import { useConfirm } from "@/components/zkconfirm";
function TableList(props) {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const confirmFn = useConfirm();
  const dicts = useDict(["department_type"]);
  const [selectedValue, setSelectedValue] = useState(
    sessionStorage.getItem("APP_TYPE") || "L3"
  );

  const [data, setData] = useState([]);
  const [rowCount, setRowCount] = useState(0);

  // 表格加载
  const [isLoading, setIsLoading] = useState(false);

  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  const [isError, setIsError] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const loadData = useCallback((value, pagination) => {
    let params = {
      applicationCode: value,
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };
    setIsLoading(true);
    try {
      getPageRoleList(params).then((res) => {
        setData(res?.data?.data || []);
        setRowCount(res.data.total || 0);
      });
    } catch {
      setIsError(true);
    } finally {
      setIsLoading(false);
      setIsRefetching(false);
    }
  }, []);

  useEffect(() => {
    loadData(selectedValue, pagination);
  }, [selectedValue, pagination.pageIndex, pagination.pageSize]);

  const handlerDelete = (id) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("common.common_delete_sure"),
    }).then(() => {
      deteleRoles(id).then((res) => {
        toast.success(res.message);

        loadData(selectedValue);
      });
    });
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: "name",
        header: t("branch.name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "departmentName",
        header: t("roles.department_name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <span>{row?.original?.name ? row?.original?.name : "-"} </span>
          );
        },
      },
      {
        accessorKey: "departmentType",
        header: t("roles.department_level"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ cell, row }) => {
          return (
            <DictTag
              dicts={dicts.current?.department_type}
              fieldName={{
                value: "value",
                title: "label",
                listClass: "listClass",
              }}
              value={row.original.departmentType}
            />
          );
        },
      },
    ],
    []
  );

  const isShowAction = {
    // isShowView: "auth:role:query",
    isShowEditor: "auth:role:update",
    isShowDetele: "auth:role:delete",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/branch", { state: { id: data?.id, type: "view" } }),
      handlerEditor: (id) =>
        navigate("/add/premission/roles", {
          state: {
            id,
            type: "editor",
            applicationCode: selectedValue,
          },
        }),
      onAdd: (data) => {
        navigate("/add/premission/roles", {
          state: {
            type: "add",
            applicationCode: selectedValue,
          },
        });
        sessionStorage.setItem("APP_TYPE", selectedValue);
      },
      Detele: (data) => {
        handlerDelete(data?.id);
      },
    }),
    [selectedValue]
  );

  const renderTable = () => {
    return (
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        loadDada={() => loadData(selectedValue, pagination)}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        topActions={{
          showAdd: "auth:role:save",
          onAdd: actionHandlers.onAdd, // 添加这一行
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("roles.title")}
        header={
          <AppTap
            isShowL3={true}
            value={selectedValue}
            setValue={setSelectedValue}></AppTap>
        }
        content={renderTable()}></LayoutList>
    </React.Fragment>
  );
}

export default TableList;
