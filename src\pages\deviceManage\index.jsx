import React from "react";
import LayoutList from "@/layout/components/LayoutList.jsx";
import TableList from "./components/TableList";
import AppTap from "@c/AppTap.jsx";
import { useTranslation } from "react-i18next";
const index = () => {
  const { t } = useTranslation();

  const [selectedValue, setSelectedValue] = useState("SD");

  const applicationList = () => {
    return (
      <AppTap
        isShowL3={false}
        value={selectedValue}
        setValue={setSelectedValue}></AppTap>
    );
  };

  const tableList = useCallback(() => {
    return (
      <TableList
        selectedValue={selectedValue}
        setSelectedValue={setSelectedValue}
      />
    );
  }, [selectedValue, setSelectedValue]);

  useEffect(() => {
    setSelectedValue(sessionStorage.getItem("APP_TYPE"));
  }, [selectedValue]);
  return (
    <React.Fragment>
      <LayoutList
        title={t("device.title")}
        header={applicationList()}
        content={tableList()}></LayoutList>
    </React.Fragment>
  );
};

export default index;
