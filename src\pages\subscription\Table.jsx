import React, { useState } from "react";
import ZktecoTable from "@c/ZktecoTable/index";
import { useNavigate } from "react-router-dom";
import LayoutList from "@l/components/LayoutList";
import UpgradeMenu from "@a/Icons/Upgrade.svg?react";
import ExtendnMenu from "@a/Icons/extend.svg?react";
import MoreMenu from "@a/Icons/More.svg?react";
import dayjs from "dayjs";
import { getSubscriptionList } from "@/service/api/subscription.js";
import { Tooltip } from "@mui/material";
import TabButton from "@c/AppTap.jsx";
import { useStateUserInfo } from "@/hooks/user";
import { debounce } from "lodash-es";
function TableList(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const userInfo = useStateUserInfo();
  const [isError, setIsError] = useState(false);
  const [selectedValue, setSelectedValue] = useState();
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  useEffect(() => {
    sessionStorage.setItem("SUB_APP", selectedValue);
  }, [selectedValue]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "contractNo",
        header: t("subscription.contractID"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "deviceCount",
        header: t("subscription.Device_Count"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "contractAmount",
        header: t("subscription.Contract_Amount"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "contractAmountUnit",
        header: t("subscription.Contract_Unit"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "packageNameI18n",
        header: t("subscription.subscriptionPackage"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: (e) => {
          return (
            <Tooltip
              title={t(`subscription.${e.row.original.packageNameI18n}`)}
              arrow
              placement="bottom">
              <span>{t(`subscription.${e.row.original.packageNameI18n}`)}</span>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "activateTime",
        header: t("subscription.subscriptionCreationDate"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        size: 250,
        Cell: (e) => {
          return (
            <Tooltip
              title={dayjs(e.row.original.activateTime).format("YYYY-MM-DD")}
              arrow
              placement="bottom">
              <span>
                {dayjs(e.row.original.activateTime).format("YYYY-MM-DD")}
              </span>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "expiraTime",
        header: t("subscription.expirationDate"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        size: 200,
        Cell: (e) => {
          return (
            <Tooltip
              title={dayjs(e.row.original.expiraTime).format("YYYY-MM-DD")}
              arrow
              placement="bottom">
              <span>
                {dayjs(e.row.original.expiraTime).format("YYYY-MM-DD")}
              </span>
            </Tooltip>
          );
        },
      },

      {
        accessorKey: "accountEmail",
        header: t("subscription.accountCreatedBy"),
        disableFilters: true,
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
    ],
    []
  );

  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      applicationCode: selectedValue,
    };

    return params;
  };

  // 获取数据
  const getTableData = useCallback(
    debounce(() => {
      if (!data.length) {
        setIsLoading(true);
      } else {
        setIsRefetching(true);
      }

      getSubscriptionList(buildParams())
        .then((res) => {
          // 设置数据
          setData(res.data.data);
          // 设置总记录数
          setRowCount(res.data.total);
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    }, 50),
    [selectedValue, pagination.pageIndex, pagination.pageSize]
  );

  useEffect(() => {
    if (!selectedValue) {
      return;
    }
    getTableData();
  }, [selectedValue, pagination.pageIndex, pagination.pageSize]);

  const [serchName, setSeachName] = useState(null);
  const handlerSeacher = () => {
    let params = {
      pageIndex: 1,
      pageSize: 5,
      name: serchName,
      applicationCode: selectedValue,
    };

    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    getSubscriptionList(params)
      .then((res) => {
        // 设置数据
        setData(res.data.data);
        // 设置总记录数
        setRowCount(res.data.total);
        setIsLoading(false);
        setIsRefetching(false);
      })
      .catch((err) => {
        setIsError(true);
        setIsLoading(false);
        setIsRefetching(false);
      });
  };

  const handleKeyDown = (event) => {
    if (event.key == "Enter") {
      handlerSeacher(); // 触发搜索操作
    }
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  return (
    <React.Fragment>
      <LayoutList
        title={t("subscription.subscription_record")}
        isSearch={false}
        onClick={handlerSeacher}
        setSeachName={setSeachName}
        handleKeyDown={handleKeyDown}
        header={
          <TabButton
            value={selectedValue}
            setValue={setSelectedValue}></TabButton>
        }
        content={
          <ZktecoTable
            showTopBar={false}
            data={data}
            loadDada={getTableData} // 刷新 方法
            columns={columns}
            enableRowActions={userInfo?.employeeType !== 2}
            showAdd={false}
            // 列数
            rowCount={rowCount}
            getRowId={(originalRow) => {
              return originalRow.devSn;
            }}
            isLoading={isLoading}
            isRefetching={isRefetching}
            isError={isError}
            totalRecords={rowCount || 0} // 如果 data 为空时避免报错
            paginationProps={{
              currentPage: pagination.pageIndex,
              rowsPerPage: pagination.pageSize,
              onPageChange: handlePageChange,
              onPageSizeChange: handlePageSizeChange,
            }}
            renderRowActions={({ cell, row, table }) => {
              return (
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                  }}>
                  {(row.original.isFree == 0 ||
                    row.original.subStatus == 3) && (
                    <Grid item>
                      <Tooltip
                        title={t("subscription.Update")}
                        arrow
                        placement="bottom">
                        <UpgradeMenu
                          onClick={() => {
                            navigate("/add/scription", {
                              state: {
                                type: "1",
                                id: row.original.id,
                                data: row.original,
                              },
                            });
                          }}
                        />
                      </Tooltip>
                    </Grid>
                  )}

                  {row.original.isFree == 1 && (
                    <Grid item>
                      <Tooltip
                        title={t("subscription.extend")}
                        arrow
                        placement="bottom">
                        <ExtendnMenu
                          onClick={() => {
                            navigate("/add/scription", {
                              state: {
                                type: "3",
                                id: row.original.id,
                                data: row.original,
                              },
                            });
                          }}
                        />
                      </Tooltip>
                    </Grid>
                  )}

                  {row.original.isFree == 1 && (
                    <Grid item>
                      <Tooltip
                        title={t("subscription.more")}
                        arrow
                        placement="bottom">
                        <MoreMenu
                          onClick={() => {
                            navigate("/add/scription", {
                              state: {
                                type: "2",
                                id: row.original.id,
                                data: row.original,
                              },
                            });
                          }}
                        />
                      </Tooltip>
                    </Grid>
                  )}
                </Grid>
              );
            }}></ZktecoTable>
        }></LayoutList>
    </React.Fragment>
  );
}

export default TableList;
