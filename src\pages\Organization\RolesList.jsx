import React from "react";
import { getAllPartRoleList } from "@s/api/premission";
function RolesList({ initialRoles, onChange, id, diabled = false }) {
  const { t } = useTranslation();

  const [roleGroups, setRoleGroups] = useState([]);
  const [selectedRoles, setSelectedRoles] = useState({});

  const [initData, setInitData] = useState(true);

  useEffect(() => {
    //  获取部门角色列表
    getAllPartRoleList({
      departmentId: id,
    }).then((res) => {
      setRoleGroups(res?.data);
    });
  }, []);

  const initialSelectedRoles = React.useMemo(() => {
    if (Array.isArray(initialRoles)) {
      return initialRoles.reduce((acc, role) => {
        acc[role.applicationId] = role.id;
        return acc;
      }, {});
    }
    return {};
  }, [initialRoles]);

  useEffect(() => {
    if (initData && initialRoles.length > 0) {
      setSelectedRoles(initialSelectedRoles);
      setInitData(false);
    }
  }, [initialRoles]);

  const handleRoleChange = (applicationId, roleId) => {
    setSelectedRoles((prev) => ({
      ...prev,
      [applicationId]: roleId,
    }));

    if (onChange) {
      onChange({
        ...selectedRoles,
        [applicationId]: roleId,
      });
    }
  };

  return (
    <React.Fragment>
      <Grid mt={4} xs={12} container>
        <Typography variant="h4" mb={2}>
          {t("roles.title")}
        </Typography>

        {roleGroups?.map((item, index) => {
          return (
            <Grid
              key={item?.id + index}
              container
              xs={12}
              sx={{
                mb: 2,
                mt: 2,
              }}>
              {item?.roleList?.length > 0 && (
                <React.Fragment>
                  <Typography
                    variant="h5"
                    style={{
                      marginBottom: "25px",
                    }}>
                    {item?.name}
                  </Typography>

                  <Grid
                    container
                    spacing={2}
                    xs={12}
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      height: "120px",
                      width: "100%",
                      background: "#f6f6f6",
                      borderRadius: "10px",
                      overflowY: "auto",
                      "&::-webkit-scrollbar": {
                        display: "none",
                      },
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}>
                    {item?.roleList?.map((role, roleIndex) => (
                      <Grid item key={role?.id} xs={3}>
                        <FormControlLabel
                          control={
                            <Radio
                              id={`role-radio-${roleIndex}`}
                              checked={selectedRoles[item.id] === role.id}
                              disabled={diabled}
                              onChange={() =>
                                handleRoleChange(item.id, role.id)
                              }
                              value={role.id}
                            />
                          }
                          label={role.name}
                          sx={{
                            textAlign: "left",
                            font: "normal normal medium 16px/20px Roboto",
                            fontWeight: "bold",
                            color: "#474B4F",
                          }}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </React.Fragment>
              )}
            </Grid>
          );
        })}
      </Grid>
    </React.Fragment>
  );
}

export default RolesList;
