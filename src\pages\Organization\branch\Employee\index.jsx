import React from "react";
import TableList from "./components/TableList";
import LayoutList from "@l/components/LayoutList";
import { getTenantUserList } from "@s/api/tenant";
import { useTranslation } from "react-i18next";
import { useTableRequest, getDepartmentCode } from "../../utils.js"; // 引入

const index = () => {
  const { t } = useTranslation();
  const [serchName, setSeachName] = useState("");
  const departmentCode = getDepartmentCode();
  const {
    data,
    isLoading,
    isRefetching,
    isError,
    rowCount,
    pagination,
    setPagination,
    fetchData,
    search,
    reset,
  } = useTableRequest(getTenantUserList, { tenantCode: departmentCode });

  // 搜索
  const handlerSeacher = () => {
    search({ name: serchName, tenantCode: departmentCode });
  };

  // 清空
  const handleClear = () => {
    setSeachName("");
    reset({ tenantCode: departmentCode });
  };

  const rederTable = () => {
    return (
      <TableList
        data={data}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        rowCount={rowCount}
        departmentCode={departmentCode}
        pagination={pagination}
        setPagination={setPagination}
        getTableData={fetchData}></TableList>
    );
  };

  return (
    <React.Fragment>
      <LayoutList
        title={t("branch_user.title")}
        isSearch={true}
        serchName={serchName}
        onClick={handlerSeacher}
        setSeachName={setSeachName}
        onClear={handleClear} // 添加
        content={rederTable()}></LayoutList>
    </React.Fragment>
  );
};

export default index;
