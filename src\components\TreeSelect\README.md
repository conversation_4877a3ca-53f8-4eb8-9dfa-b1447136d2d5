# TreeSelect - Ant Design API 兼容组件

基于 Material-UI 构建的 TreeSelect 组件，完全兼容 Ant Design TreeSelect 的 API 接口。

## 特性

✅ **完全兼容 Ant Design API** - 支持所有 Ant Design TreeSelect 的属性和方法  
✅ **Material-UI 样式** - 使用 Material-UI 组件和主题系统  
✅ **TypeScript 支持** - 完整的类型定义  
✅ **多种选择模式** - 单选、多选、可勾选模式  
✅ **异步数据加载** - 支持 loadData 异步加载  
✅ **搜索过滤** - 内置搜索功能  
✅ **自定义渲染** - 支持自定义节点渲染  
✅ **简单模式** - 支持扁平数据结构  

## 安装

```bash
npm install @mui/material @mui/lab @mui/icons-material
```

## 基本用法

```jsx
import TreeSelect from '@/components/AntdTreeSelect';

const treeData = [
  {
    title: '技术部',
    value: 'tech',
    children: [
      { title: '前端组', value: 'frontend' },
      { title: '后端组', value: 'backend' },
    ],
  },
];

function App() {
  const [value, setValue] = useState();
  
  return (
    <TreeSelect
      value={value}
      onChange={setValue}
      treeData={treeData}
      placeholder="请选择部门"
      showSearch
      allowClear
    />
  );
}
```

## API 参考

### 基础属性

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `value` | 指定当前选中的条目 | `string \| string[]` | - |
| `defaultValue` | 指定默认选中的条目 | `string \| string[]` | - |
| `onChange` | 选中树节点时调用此函数 | `function(value, label, extra)` | - |
| `treeData` | treeNodes 数据 | `TreeNode[]` | `[]` |
| `placeholder` | 选择框默认文字 | `string` | - |
| `disabled` | 是否禁用 | `boolean` | `false` |
| `allowClear` | 支持清除 | `boolean` | `false` |
| `showSearch` | 是否显示搜索框 | `boolean` | `false` |
| `multiple` | 支持多选 | `boolean` | `false` |
| `treeCheckable` | 显示 checkbox | `boolean` | `false` |

### 数据格式

```javascript
const treeData = [
  {
    title: '节点标题',
    value: '节点值',
    disabled: false,        // 可选：是否禁用
    disableCheckbox: false, // 可选：是否禁用复选框
    isLeaf: false,         // 可选：是否为叶子节点
    children: [            // 可选：子节点
      {
        title: '子节点',
        value: 'child1',
      },
    ],
  },
];
```

### 高级功能

#### 多选模式

```jsx
<TreeSelect
  multiple
  value={multipleValue}
  onChange={setMultipleValue}
  treeData={treeData}
  maxTagCount={3}
  maxTagPlaceholder={(omittedValues) => `+${omittedValues.length} 更多`}
/>
```

#### 可勾选模式

```jsx
<TreeSelect
  treeCheckable
  value={checkableValue}
  onChange={setCheckableValue}
  treeData={treeData}
  showCheckedStrategy={TreeSelect.SHOW_PARENT}
  treeCheckStrictly={false}
/>
```

#### 异步加载

```jsx
const loadData = ({ value }) => {
  return new Promise(resolve => {
    // 模拟异步请求
    setTimeout(() => {
      // 更新 treeData
      resolve();
    }, 1000);
  });
};

<TreeSelect
  treeData={asyncTreeData}
  loadData={loadData}
  value={value}
  onChange={setValue}
/>
```

#### 简单模式

```jsx
const simpleData = [
  { id: 1, pId: 0, value: '1', title: '技术部' },
  { id: 2, pId: 1, value: '1-1', title: '前端组' },
  { id: 3, pId: 1, value: '1-2', title: '后端组' },
];

<TreeSelect
  treeData={simpleData}
  treeDataSimpleMode={{
    id: 'id',
    pId: 'pId',
    rootPId: 0,
  }}
  value={value}
  onChange={setValue}
/>
```

#### 自定义字段名

```jsx
<TreeSelect
  treeData={customData}
  fieldNames={{
    label: 'name',
    value: 'key',
    children: 'items',
  }}
  value={value}
  onChange={setValue}
/>
```

### 事件回调

```jsx
<TreeSelect
  onChange={(value, label, extra) => {
    console.log('值变化:', value);
    console.log('标签:', label);
    console.log('额外信息:', extra);
  }}
  onSelect={(value, node, extra) => {
    console.log('选择节点:', value, node);
  }}
  onSearch={(searchValue) => {
    console.log('搜索:', searchValue);
  }}
  onTreeExpand={(expandedKeys) => {
    console.log('展开节点:', expandedKeys);
  }}
/>
```

### 样式定制

```jsx
<TreeSelect
  size="large"
  variant="filled"
  treeLine
  treeIcon
  prefix={<Icon />}
  suffixIcon={<CustomIcon />}
  popupClassName="custom-popup"
  dropdownStyle={{ maxHeight: 400 }}
/>
```

### 方法调用

```jsx
const treeSelectRef = useRef();

// 聚焦
treeSelectRef.current.focus();

// 失焦
treeSelectRef.current.blur();

<TreeSelect ref={treeSelectRef} />
```

## 完整 API 列表

### TreeSelect Props

| 属性 | 说明 | 类型 | 默认值 | 版本 |
|------|------|------|--------|------|
| allowClear | 支持清除 | `boolean \| { clearIcon?: ReactNode }` | `false` | - |
| autoClearSearchValue | 多选时自动清空搜索框 | `boolean` | `true` | - |
| defaultOpen | 默认展开下拉菜单 | `boolean` | - | - |
| defaultValue | 指定默认选中的条目 | `string \| string[]` | - | - |
| disabled | 是否禁用 | `boolean` | `false` | - |
| popupClassName | 下拉菜单的 className | `string` | - | - |
| dropdownRender | 自定义下拉框内容 | `(originNode: ReactNode) => ReactNode` | - | - |
| dropdownStyle | 下拉菜单的样式 | `CSSProperties` | - | - |
| fieldNames | 自定义字段名 | `object` | `{ label: 'title', value: 'value', children: 'children' }` | - |
| filterTreeNode | 是否根据输入项进行筛选 | `boolean \| function(inputValue, treeNode)` | `function` | - |
| getPopupContainer | 菜单渲染父节点 | `function(triggerNode)` | `() => document.body` | - |
| labelInValue | 是否把每个选项的 label 包装到 value 中 | `boolean` | `false` | - |
| listHeight | 设置弹窗滚动高度 | `number` | `256` | - |
| loadData | 异步加载数据 | `function(node)` | - | - |
| maxTagCount | 最多显示多少个 tag | `number \| 'responsive'` | - | - |
| maxTagPlaceholder | 隐藏 tag 时显示的内容 | `ReactNode \| function(omittedValues)` | - | - |
| maxTagTextLength | 最大显示的 tag 文本长度 | `number` | - | - |
| multiple | 支持多选 | `boolean` | `false` | - |
| notFoundContent | 当下拉列表为空时显示的内容 | `ReactNode` | `'Not Found'` | - |
| open | 是否展开下拉菜单 | `boolean` | - | - |
| placeholder | 选择框默认文字 | `string` | - | - |
| placement | 选择框弹出的位置 | `bottomLeft \| bottomRight \| topLeft \| topRight` | `bottomLeft` | - |
| searchValue | 搜索框的值 | `string` | - | - |
| showCheckedStrategy | 配置 treeCheckable 时的选中展示方式 | `TreeSelect.SHOW_ALL \| TreeSelect.SHOW_PARENT \| TreeSelect.SHOW_CHILD` | `TreeSelect.SHOW_CHILD` | - |
| showSearch | 是否显示搜索框 | `boolean` | `false` | - |
| size | 选择框大小 | `large \| middle \| small` | - | - |
| status | 设置校验状态 | `'error' \| 'warning'` | - | - |
| suffixIcon | 自定义的选择框后缀图标 | `ReactNode` | - | - |
| switcherIcon | 自定义树节点的展开/折叠图标 | `ReactNode \| ((props: AntTreeNodeProps) => ReactNode)` | - | - |
| tagRender | 自定义 tag 内容 | `(props) => ReactNode` | - | - |
| treeCheckable | 显示 checkbox | `boolean` | `false` | - |
| treeCheckStrictly | checkable 状态下节点选择完全受控 | `boolean` | `false` | - |
| treeData | treeNodes 数据 | `array<{value, title, children, [disabled, disableCheckbox, selectable, checkable]}>` | `[]` | - |
| treeDataSimpleMode | 使用简单格式的 treeData | `boolean \| object<{ id: string, pId: string, rootPId: string }>` | `false` | - |
| treeDefaultExpandAll | 默认展开所有树节点 | `boolean` | `false` | - |
| treeDefaultExpandedKeys | 默认展开的树节点 | `string[]` | - | - |
| treeExpandAction | 点击节点 title 时的展开逻辑 | `false \| 'click' \| 'doubleClick'` | `false` | - |
| treeExpandedKeys | 设置展开的树节点 | `string[]` | - | - |
| treeIcon | 是否展示 TreeNode title 前的图标 | `boolean` | `false` | - |
| treeLoadedKeys | 已经加载的节点 | `string[]` | `[]` | - |
| treeLine | 是否展示连接线 | `boolean \| object` | `false` | - |
| treeNodeFilterProp | 输入项过滤对应的 treeNode 属性 | `string` | `'value'` | - |
| treeNodeLabelProp | 作为显示的 prop 设置 | `string` | `'title'` | - |
| value | 指定当前选中的条目 | `string \| string[]` | - | - |
| variant | 形态变体 | `outlined \| borderless \| filled` | `outlined` | - |
| virtual | 设置 false 时关闭虚拟滚动 | `boolean` | `true` | - |
| onChange | 选中树节点时调用此函数 | `function(value, label, extra)` | - | - |
| onDropdownVisibleChange | 展开下拉菜单的回调 | `function(open)` | - | - |
| onSearch | 文本框值变化时回调 | `function(value: string)` | - | - |
| onSelect | 被选中时调用 | `function(value, node, extra)` | - | - |
| onTreeExpand | 展示节点时调用 | `function(expandedKeys)` | - | - |

### TreeNode Props

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| checkable | 当树为 checkable 时，设置独立节点是否展示 Checkbox | `boolean` | - |
| disableCheckbox | 禁掉 checkbox | `boolean` | `false` |
| disabled | 是否禁用 | `boolean` | `false` |
| isLeaf | 是否是叶子节点 | `boolean` | `false` |
| key | 此项必须设置（其值在整个树范围内唯一） | `string` | - |
| selectable | 是否可选 | `boolean` | `true` |
| title | 树节点显示的内容 | `ReactNode` | `'---'` |
| value | 默认根据此属性值进行筛选 | `string` | - |

## 与 Ant Design 的差异

1. **样式系统**: 使用 Material-UI 的主题系统而非 Ant Design 的样式
2. **图标**: 使用 Material-UI 的图标组件
3. **动画效果**: 使用 Material-UI 的过渡动画
4. **响应式**: 更好地适配 Material-UI 的响应式断点

## 迁移指南

从 Ant Design TreeSelect 迁移到此组件：

1. 替换导入路径
2. 保持所有 API 调用不变
3. 根据需要调整样式主题

```jsx
// 之前
import { TreeSelect } from 'antd';

// 现在
import TreeSelect from '@/components/AntdTreeSelect';

// API 完全相同
<TreeSelect
  treeData={treeData}
  value={value}
  onChange={onChange}
  placeholder="请选择"
  showSearch
  allowClear
/>
```
