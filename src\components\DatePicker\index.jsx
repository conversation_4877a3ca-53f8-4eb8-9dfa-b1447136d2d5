import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import {
  TextField,
  Popover,
  Box,
  InputAdornment,
  IconButton,
  FormHelperText,
  Button,
  Stack,
  Divider,
  Chip,
  Typography,
  useTheme,
  alpha,
} from "@mui/material";
import {
  DatePicker as MuiDatePicker,
  DateTimePicker as MuiDateTimePicker,
  LocalizationProvider,
  StaticDatePicker,
  PickersDay,
  DateCalendar,
  TimePicker,
  TimeField,
  DigitalClock,
  MultiSectionDigitalClock,
} from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import {
  CalendarToday as CalendarIcon,
  Clear as ClearIcon,
  KeyboardArrowDown as ArrowDropDownIcon,
  AccessTime as TimeIcon,
} from "@mui/icons-material";
import { styled } from '@mui/material/styles';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import RangePicker from './RangePicker';
import AntdTimePicker from './TimePicker';

// 设置 dayjs 默认语言
dayjs.locale('zh-cn');

// Material-UI 风格的样式组件
const StyledPopover = styled(Popover)(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: theme.spacing(1),
    boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    overflow: 'visible',
    minWidth: 'auto',
    background: theme.palette.background.paper,
    pointerEvents: 'auto',
    zIndex: theme.zIndex.modal,
  },
  '& .MuiPopover-paper': {
    pointerEvents: 'auto',
    overflow: 'visible',
  },
}));

const StyledDateCalendar = styled(DateCalendar)(({ theme }) => ({
  width: 280,
  height: 'auto',
  margin: 0,
  padding: theme.spacing(1),
  pointerEvents: 'auto',
  '& *': {
    pointerEvents: 'auto',
  },
  '& .MuiPickersCalendarHeader-root': {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
    marginTop: 0,
    marginBottom: theme.spacing(1),
  },
  '& .MuiPickersCalendarHeader-label': {
    fontSize: '0.875rem',
    fontWeight: 500,
  },
  '& .MuiDayCalendar-header': {
    paddingLeft: theme.spacing(1),
    paddingRight: theme.spacing(1),
  },
  '& .MuiDayCalendar-weekDayLabel': {
    fontSize: '0.75rem',
    fontWeight: 400,
    color: theme.palette.text.secondary,
    width: 32,
    height: 32,
    margin: 0,
  },
  '& .MuiDayCalendar-slideTransition': {
    minHeight: 240,
  },
  '& .MuiDayCalendar-monthContainer': {
    position: 'relative',
  },
  '& .MuiDayCalendar-weekContainer': {
    margin: 0,
    justifyContent: 'space-around',
  },
  '& .MuiPickersDay-root': {
    fontSize: '0.875rem',
    width: 32,
    height: 32,
    margin: '2px',
    borderRadius: theme.spacing(0.5),
    cursor: 'pointer !important',
    position: 'relative',
    zIndex: 1,
    pointerEvents: 'auto',
    '&:hover': {
      backgroundColor: `${alpha(theme.palette.primary.main, 0.08)} !important`,
    },
    '&.Mui-selected': {
      backgroundColor: `${theme.palette.primary.main} !important`,
      color: `${theme.palette.primary.contrastText} !important`,
      '&:hover': {
        backgroundColor: `${theme.palette.primary.dark} !important`,
      },
    },
    '&.MuiPickersDay-today': {
      border: `1px solid ${theme.palette.primary.main}`,
      backgroundColor: 'transparent',
      '&:not(.Mui-selected)': {
        color: theme.palette.primary.main,
      },
    },
    '&.Mui-disabled': {
      color: theme.palette.text.disabled,
      cursor: 'not-allowed',
      pointerEvents: 'none',
    },
  },
  // 年份和月份选择器样式
  '& .MuiYearCalendar-root': {
    width: '100%',
    height: 300,
    maxHeight: 300,
    overflow: 'auto',
    padding: theme.spacing(1),
    '&::-webkit-scrollbar': {
      width: 6,
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: alpha(theme.palette.text.secondary, 0.3),
      borderRadius: 3,
    },
  },
  '& .MuiPickersYear-root': {
    fontSize: '0.875rem',
    height: 36,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
    },
    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.primary.dark,
      },
    },
  },
  '& .MuiMonthCalendar-root': {
    width: '100%',
    height: 300,
    padding: theme.spacing(1),
  },
  '& .MuiPickersMonth-root': {
    fontSize: '0.875rem',
    height: 36,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
    },
    '&.Mui-selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        backgroundColor: theme.palette.primary.dark,
      },
    },
  },
}));

const StyledTimePanel = styled(Box)(({ theme }) => ({
  display: 'flex',
  borderTop: `1px solid ${theme.palette.divider}`,
  '& .time-column': {
    flex: 1,
    borderRight: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderRight: 'none',
    },
  },
  '& .time-column-header': {
    padding: theme.spacing(1),
    textAlign: 'center',
    fontSize: '0.75rem',
    fontWeight: 500,
    color: theme.palette.text.secondary,
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: alpha(theme.palette.background.default, 0.5),
  },
  '& .time-list': {
    height: 200,
    overflowY: 'auto',
    '&::-webkit-scrollbar': {
      width: 6,
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: alpha(theme.palette.text.secondary, 0.3),
      borderRadius: 3,
    },
  },
  '& .time-item': {
    padding: theme.spacing(0.5, 1),
    textAlign: 'center',
    fontSize: '0.875rem',
    cursor: 'pointer',
    transition: theme.transitions.create(['background-color'], {
      duration: theme.transitions.duration.shortest,
    }),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.08),
    },
    '&.selected': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
    },
    '&.disabled': {
      color: theme.palette.text.disabled,
      cursor: 'not-allowed',
      '&:hover': {
        backgroundColor: 'transparent',
      },
    },
  },
}));

const PresetButton = styled(Button)(({ theme }) => ({
  justifyContent: 'flex-start',
  textTransform: 'none',
  fontSize: '0.875rem',
  padding: theme.spacing(0.5, 1),
  minHeight: 32,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
  },
}));

/**
 * DatePicker 组件 - 兼容 Ant Design DatePicker API
 */
const DatePicker = ({
  // 基础属性
  value,
  defaultValue,
  onChange,
  onOk,
  onOpenChange,
  onPanelChange,

  // 显示相关
  placeholder = "请选择日期",
  size = "middle",
  variant = "outlined",
  disabled = false,
  allowClear = true,
  autoFocus = false,

  // 格式化
  format = "YYYY-MM-DD",

  // 下拉框相关
  open,
  defaultOpen = false,
  getPopupContainer,
  popupClassName,
  popupStyle,
  placement = "bottomLeft",

  // 时间选择
  showTime = false,
  showNow = true,

  // 日期限制
  disabledDate,
  disabledTime,
  minDate,
  maxDate,

  // 选择器类型
  picker = "date", // date | week | month | quarter | year
  mode,

  // 多选
  multiple = false,

  // 面板相关
  renderExtraFooter,
  cellRender,
  panelRender,

  // 预设
  presets = [],

  // 图标
  suffixIcon,
  prefix,

  // 状态
  status,

  // 国际化
  locale,

  // 其他
  className,
  style,
  inputReadOnly = false,
  preserveInvalidOnBlur = false,
  needConfirm = false,

  ...restProps
}) => {
  // 状态管理
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [internalValue, setInternalValue] = useState(defaultValue);
  const [anchorEl, setAnchorEl] = useState(null);
  const [tempValue, setTempValue] = useState(null);

  const inputRef = useRef(null);
  const theme = useTheme();

  // 受控状态处理
  const isControlledOpen = open !== undefined;
  const isControlledValue = value !== undefined;

  const currentOpen = isControlledOpen ? open : internalOpen;
  const currentValue = isControlledValue ? value : internalValue;

  // 处理打开状态变化
  const handleOpenChange = useCallback((newOpen) => {
    if (!isControlledOpen) {
      setInternalOpen(newOpen);
    }

    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  }, [isControlledOpen, onOpenChange]);

  // 处理值变化
  const handleValueChange = useCallback((newValue, triggerEvent = 'change') => {
    const formattedValue = newValue ? dayjs(newValue) : null;
    const dateString = formattedValue ? formattedValue.format(format) : '';

    if (!isControlledValue) {
      setInternalValue(formattedValue);
    }

    if (onChange) {
      onChange(formattedValue, dateString);
    }

    // 如果不需要确认，直接关闭面板
    if (!needConfirm && !showTime && triggerEvent === 'select') {
      handleOpenChange(false);
    }
  }, [isControlledValue, onChange, format, needConfirm, showTime, handleOpenChange]);

  // 处理确认
  const handleOk = useCallback(() => {
    if (tempValue !== null) {
      handleValueChange(tempValue, 'ok');
      setTempValue(null);
    }
    handleOpenChange(false);

    if (onOk) {
      onOk();
    }
  }, [tempValue, handleValueChange, handleOpenChange, onOk]);

  // 处理取消
  const handleCancel = useCallback(() => {
    setTempValue(null);
    handleOpenChange(false);
  }, [handleOpenChange]);

  // 处理清除
  const handleClear = useCallback((e) => {
    e.stopPropagation();
    handleValueChange(null, 'clear');
  }, [handleValueChange]);

  // 处理输入框点击
  const handleInputClick = useCallback((e) => {
    if (!disabled) {
      setAnchorEl(e.currentTarget);
      handleOpenChange(true);
    }
  }, [disabled, handleOpenChange]);

  // 获取显示文本
  const getDisplayText = useCallback(() => {
    if (!currentValue) return "";

    if (multiple && Array.isArray(currentValue)) {
      return currentValue.map(val => dayjs(val).format(format)).join(", ");
    }

    return dayjs(currentValue).format(format);
  }, [currentValue, format, multiple]);

  // 获取输入框尺寸
  const getInputSize = () => {
    switch (size) {
      case 'large': return 'medium';
      case 'small': return 'small';
      default: return 'medium';
    }
  };

  // 判断日期是否禁用
  const isDateDisabled = useCallback((date) => {
    if (disabledDate) {
      return disabledDate(dayjs(date));
    }

    if (minDate && dayjs(date).isBefore(dayjs(minDate), 'day')) {
      return true;
    }

    if (maxDate && dayjs(date).isAfter(dayjs(maxDate), 'day')) {
      return true;
    }

    return false;
  }, [disabledDate, minDate, maxDate]);

  // 渲染预设按钮
  const renderPresets = () => {
    if (!presets || presets.length === 0) return null;

    return (
      <Box sx={{ p: 1, borderRight: `1px solid ${theme.palette.divider}`, minWidth: 120 }}>
        <Stack spacing={0.5}>
          {presets.map((preset, index) => (
            <PresetButton
              key={index}
              variant="text"
              size="small"
              fullWidth
              onClick={() => {
                const presetValue = typeof preset.value === 'function' ? preset.value() : preset.value;
                handleValueChange(presetValue, 'preset');
              }}
            >
              {preset.label}
            </PresetButton>
          ))}
        </Stack>
      </Box>
    );
  };

  // 生成时间选项
  const generateTimeOptions = useCallback((type) => {
    const options = [];
    let max = 24;
    let step = 1;

    if (type === 'hour') {
      max = 24;
      step = 1;
    } else if (type === 'minute') {
      max = 60;
      step = 1;
    } else if (type === 'second') {
      max = 60;
      step = 1;
    }

    for (let i = 0; i < max; i += step) {
      const value = i.toString().padStart(2, '0');
      const disabled = false; // 可以在这里添加禁用逻辑
      options.push({ value: i, label: value, disabled });
    }

    return options;
  }, []);

  // 渲染时间选择面板
  const renderTimePanel = () => {
    const timeValue = needConfirm ? (tempValue || currentValue) : currentValue;
    const currentTime = timeValue ? dayjs(timeValue) : dayjs();

    const hours = generateTimeOptions('hour');
    const minutes = generateTimeOptions('minute');
    const seconds = generateTimeOptions('second');

    const handleTimeChange = (type, value) => {
      let newTime = currentTime.clone();

      if (type === 'hour') {
        newTime = newTime.hour(value);
      } else if (type === 'minute') {
        newTime = newTime.minute(value);
      } else if (type === 'second') {
        newTime = newTime.second(value);
      }

      if (needConfirm) {
        setTempValue(newTime);
      } else {
        handleValueChange(newTime, 'select');
      }
    };

    return (
      <Box sx={{ minWidth: 240 }}>
        <StyledTimePanel>
          {/* 小时列 */}
          <Box className="time-column">
            <Box className="time-column-header">时</Box>
            <Box className="time-list">
              {hours.map((hour) => (
                <Box
                  key={hour.value}
                  className={`time-item ${currentTime.hour() === hour.value ? 'selected' : ''} ${hour.disabled ? 'disabled' : ''}`}
                  onClick={() => !hour.disabled && handleTimeChange('hour', hour.value)}
                >
                  {hour.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 分钟列 */}
          <Box className="time-column">
            <Box className="time-column-header">分</Box>
            <Box className="time-list">
              {minutes.map((minute) => (
                <Box
                  key={minute.value}
                  className={`time-item ${currentTime.minute() === minute.value ? 'selected' : ''} ${minute.disabled ? 'disabled' : ''}`}
                  onClick={() => !minute.disabled && handleTimeChange('minute', minute.value)}
                >
                  {minute.label}
                </Box>
              ))}
            </Box>
          </Box>

          {/* 秒列 */}
          <Box className="time-column">
            <Box className="time-column-header">秒</Box>
            <Box className="time-list">
              {seconds.map((second) => (
                <Box
                  key={second.value}
                  className={`time-item ${currentTime.second() === second.value ? 'selected' : ''} ${second.disabled ? 'disabled' : ''}`}
                  onClick={() => !second.disabled && handleTimeChange('second', second.value)}
                >
                  {second.label}
                </Box>
              ))}
            </Box>
          </Box>
        </StyledTimePanel>

        {/* 确认按钮 */}
        {needConfirm && (
          <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}`, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button size="small" onClick={handleCancel}>
              取消
            </Button>
            <Button size="small" variant="contained" onClick={handleOk}>
              确定
            </Button>
          </Box>
        )}
      </Box>
    );
  };

  // 渲染日期面板
  const renderDatePanel = () => {
    const dateValue = needConfirm ? (tempValue || currentValue) : currentValue;

    return (
      <Box sx={{ display: 'flex' }}>
        {renderPresets()}
        <Box sx={{ display: 'flex', flexDirection: showTime ? 'row' : 'column' }}>
          {/* 日期选择部分 */}
          <Box>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="zh-cn">
              <StyledDateCalendar
                value={dateValue}
                onChange={(newValue) => {
                  console.log('DateCalendar onChange:', newValue);
                  if (needConfirm || showTime) {
                    setTempValue(newValue);
                  } else {
                    handleValueChange(newValue, 'select');
                  }
                }}
                shouldDisableDate={isDateDisabled}
                views={picker === 'year' ? ['year'] :
                       picker === 'month' ? ['year', 'month'] :
                       ['year', 'month', 'day']}
                openTo={picker === 'year' ? 'year' :
                       picker === 'month' ? 'month' : 'day'}
                displayWeekNumber={false}
                fixedWeekNumber={6}
              />
            </LocalizationProvider>
          </Box>

          {/* 时间选择部分 */}
          {showTime && (
            <Box sx={{ borderLeft: `1px solid ${theme.palette.divider}` }}>
              {renderTimePanel()}
            </Box>
          )}



          {/* 额外的页脚 */}
          {renderExtraFooter && (
            <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
              {renderExtraFooter(mode)}
            </Box>
          )}

          {/* 确认按钮 */}
          {(needConfirm || showTime) && (
            <Box sx={{ p: 1, borderTop: `1px solid ${theme.palette.divider}`, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button size="small" onClick={handleCancel}>
                取消
              </Button>
              <Button size="small" variant="contained" onClick={handleOk}>
                确定
              </Button>
            </Box>
          )}
        </Box>
      </Box>
    );
  };

  return (
    <Box className={className} style={style}>
      {/* 输入框 */}
      <TextField
        ref={inputRef}
        value={getDisplayText()}
        placeholder={placeholder}
        size={getInputSize()}
        variant={variant}
        disabled={disabled}
        error={status === 'error'}
        autoFocus={autoFocus}
        fullWidth
        onClick={handleInputClick}
        InputProps={{
          readOnly: true,
          style: { cursor: disabled ? 'default' : 'pointer' },
          startAdornment: prefix && (
            <InputAdornment position="start">
              {prefix}
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {allowClear && currentValue && !disabled && (
                <IconButton
                  size="small"
                  onClick={handleClear}
                  sx={{ mr: 0.5 }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              {suffixIcon || (showTime ? <TimeIcon /> : <CalendarIcon />)}
            </InputAdornment>
          ),
        }}
        {...restProps}
      />

      {/* 下拉日历面板 */}
      <StyledPopover
        open={currentOpen}
        anchorEl={anchorEl}
        onClose={() => {
          setAnchorEl(null);
          handleOpenChange(false);
        }}
        anchorOrigin={{
          vertical: placement.includes('top') ? 'top' : 'bottom',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        transformOrigin={{
          vertical: placement.includes('top') ? 'bottom' : 'top',
          horizontal: placement.includes('Right') ? 'right' : 'left',
        }}
        className={popupClassName}
        slotProps={{
          paper: {
            style: {
              ...popupStyle,
              pointerEvents: 'auto',
            },
          },
        }}
        sx={{
          '& .MuiPopover-paper': {
            pointerEvents: 'auto',
          },
        }}
      >
        {panelRender ? panelRender(
          picker === 'time' ? renderTimePanel() : renderDatePanel()
        ) : (
          picker === 'time' ? renderTimePanel() : renderDatePanel()
        )}
      </StyledPopover>

      {/* 状态提示 */}
      {status === 'warning' && (
        <FormHelperText sx={{ color: 'warning.main' }}>
          警告状态
        </FormHelperText>
      )}
    </Box>
  );
};

// 添加 RangePicker 和 TimePicker 作为 DatePicker 的静态属性
DatePicker.RangePicker = RangePicker;
DatePicker.TimePicker = AntdTimePicker;

// 添加 ref 方法支持
const DatePickerWithRef = React.forwardRef((props, ref) => {
  const datePickerRef = useRef();

  React.useImperativeHandle(ref, () => ({
    focus: () => {
      datePickerRef.current?.focus();
    },
    blur: () => {
      datePickerRef.current?.blur();
    },
  }));

  return <DatePicker {...props} ref={datePickerRef} />;
});

// 保持静态属性
DatePickerWithRef.RangePicker = RangePicker;
DatePickerWithRef.TimePicker = AntdTimePicker;

export default DatePickerWithRef;
