/**
 * 微前端导航处理器
 * 处理浏览器前进后退导航，确保微前端应用路由状态正确
 */

import {
  getAppNameFromPath,
  isAppRootPath,
  getAppDefaultRoute,
  saveAppRouteState,
  clearAppRouteState
} from './microAppRouteManager';
import globalLoadingManager from './globalLoadingManager';
import pageRefreshOverlay from './pageRefreshOverlay';

// 导航配置
const NAVIGATION_CONFIG = {
  // 是否在从微前端返回主应用时强制刷新页面
  forceRefreshOnReturnToMain: false, // 改为智能刷新模式
  // 刷新延迟时间（毫秒）
  refreshDelay: 100,
  // 路径恢复延迟时间（毫秒）
  restoreDelay: 100,
  // 是否使用平滑刷新（减少视觉闪烁）
  useSmoothRefresh: true,
  // 平滑刷新的淡出时间（毫秒）
  smoothRefreshFadeTime: 200,
  // 是否使用智能刷新（仅在必要时刷新）
  useSmartRefresh: true,
  // 平滑过渡时间（毫秒）
  transitionDuration: 300,
  // 是否启用预加载
  enablePreload: true,
  // 智能刷新检测配置
  smartRefreshConfig: {
    // 检测微前端容器是否有残留内容
    checkMicroAppContainer: true,
    // 检测全局状态污染
    checkGlobalStatePollution: true,
    // 检测样式污染
    checkStylePollution: true,
    // 检测内存泄漏
    checkMemoryLeaks: true,
    // 最大连续失败次数，超过后强制刷新
    maxConsecutiveFailures: 2,
    // 应用中心页面特殊处理
    applicationCenterSpecialHandling: true
  }
};

// 导航状态管理
let isNavigationHandlerActive = false;
let lastKnownPath = '';
let navigationHistory = [];
let lastMicroAppPath = ''; // 记录上次访问的微前端路径

/**
 * 记录导航历史
 * @param {string} path - 路径
 * @param {string} action - 动作类型 (PUSH, REPLACE, POP)
 */
const recordNavigation = (path, action = 'UNKNOWN') => {
  const record = {
    path,
    action,
    timestamp: Date.now(),
    appName: getAppNameFromPath(path)
  };

  navigationHistory.push(record);

  // 保持历史记录在合理范围内
  if (navigationHistory.length > 50) {
    navigationHistory.shift();
  }

  // console.log(`📍 导航记录: ${action} -> ${path}`, record);
};

/**
 * 获取导航历史
 * @returns {Array} 导航历史记录
 */
export const getNavigationHistory = () => {
  return [...navigationHistory];
};

/**
 * 清除导航历史
 */
export const clearNavigationHistory = () => {
  navigationHistory = [];
};

/**
 * 创建平滑过渡遮罩
 */
const createSmoothTransitionOverlay = () => {
  const overlay = document.createElement('div');
  overlay.id = 'smooth-transition-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 9999;
    opacity: 0;
    transition: opacity ${NAVIGATION_CONFIG.transitionDuration}ms ease-in-out;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;

  // 添加优雅的加载指示器
  const loadingContainer = document.createElement('div');
  loadingContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
  `;

  const loadingIndicator = document.createElement('div');
  loadingIndicator.style.cssText = `
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  `;

  const loadingText = document.createElement('div');
  loadingText.textContent = '正在切换应用...';
  loadingText.style.cssText = `
    font-size: 16px;
    font-weight: 500;
    opacity: 0.9;
  `;

  // 添加旋转动画
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);

  loadingContainer.appendChild(loadingIndicator);
  loadingContainer.appendChild(loadingText);
  overlay.appendChild(loadingContainer);
  document.body.appendChild(overlay);

  return overlay;
};

/**
 * 移除平滑过渡遮罩
 */
const removeSmoothTransitionOverlay = () => {
  const overlay = document.getElementById('smooth-transition-overlay');
  if (overlay) {
    overlay.style.opacity = '0';
    setTimeout(() => {
      overlay.remove();
    }, NAVIGATION_CONFIG.transitionDuration);
  }
};

/**
 * 执行平滑过渡
 */
const performSmoothTransition = (callback, fromApp = null, toApp = null) => {
  // 检查回调是否包含页面刷新
  const isPageRefresh = callback.toString().includes('window.location.reload');

  if (!NAVIGATION_CONFIG.useSmoothRefresh && !isPageRefresh) {
    // 直接执行回调
    callback();
    return;
  }

  // 使用全局加载管理器
  if (isPageRefresh) {
    // 页面刷新时使用专门的页面刷新加载器
    globalLoadingManager.startPageRefresh();

    // 页面刷新时延迟较短，确保加载遮罩显示并开始进度动画
    setTimeout(() => {
      callback();
    }, 200);
  } else {
    // 普通应用切换
    if (fromApp || toApp) {
      globalLoadingManager.startAppTransition(fromApp, toApp);
    } else {
      globalLoadingManager.startLoading({
        message: '正在切换应用...',
        showProgress: true,
        type: 'transition'
      });
    }

    // 等待过渡完成后执行回调
    setTimeout(() => {
      callback();
      // 延迟完成加载，确保新页面已经渲染
      setTimeout(() => {
        globalLoadingManager.finishLoading();
      }, 200);
    }, NAVIGATION_CONFIG.transitionDuration);
  }
};

/**
 * 智能刷新页面（仅在必要时刷新）
 */
const performSmartRefresh = () => {
  // 检查是否刚刚进行了语言切换
  const isLanguageSwitching = checkIfLanguageSwitching();

  // 如果启用了强制刷新，直接刷新页面
  if (NAVIGATION_CONFIG.forceRefreshOnReturnToMain) {
    console.log('🔄 从微前端返回主应用，执行页面刷新');

    // 使用专门的页面刷新加载器，提供更好的用户体验
    globalLoadingManager.startPageRefresh();

    // 延迟执行刷新，确保加载遮罩已显示并开始进度动画
    setTimeout(() => {
      window.location.reload();
    }, 200);

    return;
  }

  // 智能刷新逻辑
  if (!NAVIGATION_CONFIG.useSmartRefresh) {
    // 同样使用页面刷新加载器
    globalLoadingManager.startPageRefresh();

    setTimeout(() => {
      window.location.reload();
    }, 200);
    return;
  }

  // 检查是否真的需要刷新
  const needsRefresh = checkIfRefreshNeeded();

  if (needsRefresh || isLanguageSwitching) {
    if (isLanguageSwitching) {
      console.log('🌐 检测到语言切换，需要刷新页面以确保同步');
    } else {
      console.log('🔄 智能检测：需要刷新页面');
    }

    // 显示加载遮罩后刷新
    globalLoadingManager.startPageRefresh();

    setTimeout(() => {
      window.location.reload();
    }, 200);
  } else {
    console.log('✅ 智能检测：无需刷新，执行清理操作');
    // 不需要刷新，只需要清理微前端状态
    const cleanupSuccess = cleanupMicroAppState();

    // 如果清理失败，可能需要刷新
    if (!cleanupSuccess) {
      console.warn('⚠️ 清理失败，回退到页面刷新');
      globalLoadingManager.startPageRefresh();
      setTimeout(() => {
        window.location.reload();
      }, 200);
    }
  }
};

// 连续失败计数器
let consecutiveFailures = 0;

// 语言切换检测
let lastLanguageChangeTime = 0;

/**
 * 检查是否刚刚进行了语言切换
 */
const checkIfLanguageSwitching = () => {
  const now = Date.now();
  const timeSinceLastChange = now - lastLanguageChangeTime;

  // 如果在过去5秒内有语言切换，认为是语言切换导致的导航
  const isRecentLanguageChange = timeSinceLastChange < 5000;

  if (isRecentLanguageChange) {
    console.log(`🌐 检测到最近的语言切换 (${timeSinceLastChange}ms 前)`);
    return true;
  }

  return false;
};

/**
 * 记录语言切换时间
 */
const recordLanguageChange = () => {
  lastLanguageChangeTime = Date.now();
  console.log('🌐 记录语言切换时间');
};

/**
 * 检查是否需要刷新页面（增强版）
 */
const checkIfRefreshNeeded = () => {
  const config = NAVIGATION_CONFIG.smartRefreshConfig;
  const issues = [];

  // 1. 检查微前端容器是否有残留内容
  if (config.checkMicroAppContainer) {
    const microAppContainer = document.getElementById('sub-app-container');
    if (microAppContainer && microAppContainer.innerHTML.trim()) {
      issues.push('微前端容器有残留内容');
    }
  }

  // 2. 检查全局状态污染
  if (config.checkGlobalStatePollution) {
    if (window.__POWERED_BY_QIANKUN__) {
      issues.push('qiankun全局状态污染');
    }

    // 检查其他可能的全局状态污染
    const globalKeys = ['__MICRO_APP_ENVIRONMENT__', '__MICRO_APP_NAME__'];
    globalKeys.forEach(key => {
      if (window[key]) {
        issues.push(`全局状态污染: ${key}`);
      }
    });
  }

  // 3. 检查样式污染
  if (config.checkStylePollution) {
    const head = document.head;
    const microAppStyles = head.querySelectorAll('style[data-qiankun], link[data-qiankun]');
    if (microAppStyles.length > 0) {
      issues.push(`样式污染: ${microAppStyles.length}个残留样式`);
    }
  }

  // 4. 检查内存泄漏指标
  if (config.checkMemoryLeaks) {
    // 检查是否有过多的事件监听器
    const eventListenerCount = Object.keys(window).filter(key =>
      key.startsWith('on') || key.includes('listener')
    ).length;

    if (eventListenerCount > 50) { // 阈值可调整
      issues.push(`可能的内存泄漏: ${eventListenerCount}个事件监听器`);
    }
  }

  // 5. 检查连续失败次数
  if (consecutiveFailures >= config.maxConsecutiveFailures) {
    issues.push(`连续失败次数过多: ${consecutiveFailures}`);
    consecutiveFailures = 0; // 重置计数器
  }

  // 记录检测结果
  if (issues.length > 0) {
    console.warn('🔍 检测到需要刷新的问题:', issues);
    consecutiveFailures++;
    return true;
  } else {
    console.log('✅ 智能检测通过，无需刷新页面');
    consecutiveFailures = 0; // 重置失败计数器
    return false;
  }
};

/**
 * 确保微前端容器存在
 */
const ensureMicroAppContainer = () => {
  let container = document.getElementById('sub-app-container');

  if (!container) {
    console.log('🔧 微前端容器不存在，正在创建...');

    // 查找合适的父容器
    const appRoot = document.getElementById('root') || document.body;

    // 创建容器
    container = document.createElement('div');
    container.id = 'sub-app-container';
    container.style.cssText = `
      width: 100%;
      height: 100%;
      min-height: inherit;
      display: block;
      position: relative;
    `;

    // 添加到DOM中
    appRoot.appendChild(container);
    console.log('✅ 微前端容器已创建');
  }

  return container;
};

/**
 * 清理微前端应用状态（增强版）
 */
const cleanupMicroAppState = () => {
  console.log('🧹 开始清理微前端状态...');

  try {
    // 1. 清理微前端容器内容，但保留容器本身
    const microAppContainer = document.getElementById('sub-app-container');
    if (microAppContainer) {
      // 先移除所有子元素
      while (microAppContainer.firstChild) {
        microAppContainer.removeChild(microAppContainer.firstChild);
      }
      // 清空innerHTML确保完全清空
      microAppContainer.innerHTML = '';

      // 重置容器样式，确保下次使用时状态正确
      microAppContainer.style.cssText = `
        width: 100%;
        height: 100%;
        min-height: inherit;
        display: block;
        position: relative;
        opacity: 1;
        transition: opacity 0.3s ease-in-out;
      `;

      console.log('✅ 微前端容器内容已清理，样式已重置');
    } else {
      console.log('⚠️ 微前端容器不存在，将在需要时重新创建');
    }

    // 2. 清理全局状态
    const globalKeysToClean = [
      '__POWERED_BY_QIANKUN__',
      '__MICRO_APP_ENVIRONMENT__',
      '__MICRO_APP_NAME__',
      '__MICRO_APP_PUBLIC_PATH__'
    ];

    globalKeysToClean.forEach(key => {
      if (window[key]) {
        delete window[key];
        console.log(`✅ 已清理全局状态: ${key}`);
      }
    });

    // 3. 如果qiankun已经加载，尝试卸载所有子应用
    if (window.unmountMicroApp) {
      try {
        // 卸载所有可能的微应用
        const appNames = ['cms-app', 'retail-ai-app', 'e-price-tag-app'];
        appNames.forEach(appName => {
          try {
            window.unmountMicroApp(appName);
            console.log(`✅ 已卸载微应用: ${appName}`);
          } catch (e) {
            console.warn(`⚠️ 卸载微应用失败: ${appName}`, e);
          }
        });
      } catch (e) {
        console.warn('⚠️ 批量卸载微应用时出错:', e);
      }
    }

    // 4. 清理可能残留的样式
    const head = document.head;
    const microAppStyles = head.querySelectorAll('style[data-qiankun], link[data-qiankun]');
    microAppStyles.forEach(style => {
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    });

    if (microAppStyles.length > 0) {
      console.log(`✅ 已清理 ${microAppStyles.length} 个残留样式`);
    }

    // 5. 清理可能的事件监听器
    const eventsToClean = ['microapp-state-change', 'microapp-route-change'];
    eventsToClean.forEach(eventType => {
      // 移除可能的事件监听器（这里只是示例，实际需要根据具体实现调整）
      const listeners = window._microAppEventListeners?.[eventType];
      if (listeners && Array.isArray(listeners)) {
        listeners.forEach(listener => {
          window.removeEventListener(eventType, listener);
        });
        delete window._microAppEventListeners[eventType];
      }
    });

    // 6. 强制垃圾回收（如果可用）
    if (window.gc && typeof window.gc === 'function') {
      try {
        window.gc();
        console.log('✅ 已触发垃圾回收');
      } catch (e) {
        // 忽略垃圾回收错误
      }
    }

    // 7. 触发主应用重新渲染和状态同步
    window.dispatchEvent(new CustomEvent('microapp-cleanup', {
      detail: { timestamp: Date.now(), source: 'navigation-handler' }
    }));

    // 8. 特殊处理：如果目标是应用中心页面，触发数据重新加载
    const currentPath = window.location.pathname;
    if (currentPath === '/application/center') {
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('application-center-refresh', {
          detail: { reason: 'microapp-cleanup' }
        }));
      }, 100);
    }

    console.log('✅ 微前端状态清理完成');
    return true;

  } catch (error) {
    console.error('❌ 清理微前端状态时出错:', error);
    return false;
  }
};

/**
 * 检查是否需要特殊处理
 * @param {string} newPath - 新路径
 * @param {string} oldPath - 旧路径
 * @param {string} action - 动作类型
 * @returns {string} 处理类型: 'none', 'cleanup', 'refresh'
 */
const getNavigationHandlingType = (newPath, oldPath, action) => {
  const newAppName = getAppNameFromPath(newPath);
  const oldAppName = getAppNameFromPath(oldPath);

  // 只在浏览器后退/前进时检查
  if (action !== 'POP') {
    return 'none';
  }

  // 如果从微前端应用返回到主应用
  if (oldAppName && !newAppName) {
    console.log(`🔙 检测到从微前端应用 ${oldAppName} 返回主应用`);
    // 如果启用了强制刷新
    if (NAVIGATION_CONFIG.forceRefreshOnReturnToMain) {
      console.log('✅ 启用了强制刷新，将执行页面刷新');
      return 'refresh';
    }
    // 否则只需要清理状态
    console.log('⚠️ 未启用强制刷新，仅清理状态');
    return 'cleanup';
  }

  return 'none';
};

/**
 * 处理路径变化
 * @param {string} newPath - 新路径
 * @param {string} action - 动作类型
 */
const handlePathChange = (newPath, action = 'POP') => {
  if (newPath === lastKnownPath) {
    return; // 路径没有变化，跳过处理
  }

  const oldPath = lastKnownPath;
  lastKnownPath = newPath;

  recordNavigation(newPath, action);

  const newAppName = getAppNameFromPath(newPath);
  const oldAppName = getAppNameFromPath(oldPath);

  // console.log(`🔄 路径变化: ${oldPath} -> ${newPath}`, {
  //   oldAppName,
  //   newAppName,
  //   action,
  //   isRootPath: isAppRootPath(newPath)
  // });

  // 检查需要的处理类型
  const handlingType = getNavigationHandlingType(newPath, oldPath, action);

  if (handlingType === 'refresh') {
    console.log(`🔄 触发页面刷新: ${oldPath} -> ${newPath}`);

    // 立即显示加载遮罩，在路径变化之前
    globalLoadingManager.startPageRefresh();

    // 保存当前路径到sessionStorage，刷新后恢复
    sessionStorage.setItem('microapp_target_path', newPath);

    // 延迟刷新，确保加载遮罩已显示
    setTimeout(() => {
      console.log('⏰ 执行延迟刷新');
      window.location.reload();
    }, 300); // 给加载遮罩足够时间显示

    return;
  } else if (handlingType === 'cleanup') {
    // console.log(`🧹 清理微前端状态: ${oldPath} -> ${newPath}`);

    // 执行平滑过渡和状态清理
    performSmoothTransition(() => {
      cleanupMicroAppState();
    });

    return;
  }

  // 更新微前端路径记录
  if (newAppName) {
    lastMicroAppPath = newPath;
  }

  // 如果从一个微前端应用切换到另一个应用
  if (oldAppName && newAppName && oldAppName !== newAppName) {
    // console.log(`🔄 应用切换: ${oldAppName} -> ${newAppName}`);

    // 保存旧应用的路由状态
    if (oldPath) {
      saveAppRouteState(oldAppName, oldPath);
    }

    // 如果新路径是根路径，需要重定向到默认首页
    if (isAppRootPath(newPath)) {
      const defaultRoute = getAppDefaultRoute(newAppName);
      // console.log(`🏠 重定向到默认首页: ${defaultRoute}`);

      // 使用 replaceState 避免在历史记录中添加额外条目
      window.history.replaceState(null, '', defaultRoute);
      lastKnownPath = defaultRoute;
      recordNavigation(defaultRoute, 'REPLACE');
      return;
    }
  }

  // 如果是微前端应用的根路径访问
  if (newAppName && isAppRootPath(newPath)) {
    const defaultRoute = getAppDefaultRoute(newAppName);
    // console.log(`🏠 根路径访问，重定向到: ${defaultRoute}`);

    window.history.replaceState(null, '', defaultRoute);
    lastKnownPath = defaultRoute;
    recordNavigation(defaultRoute, 'REPLACE');
    return;
  }

  // 保存当前应用的路由状态
  if (newAppName) {
    saveAppRouteState(newAppName, newPath);
  }
};

/**
 * popstate 事件处理器
 * @param {PopStateEvent} event - popstate 事件
 */
const handlePopState = (event) => {
  const currentPath = window.location.pathname;
  const oldPath = lastKnownPath;

  // 早期检测：如果从微前端应用返回主应用，立即显示加载遮罩
  const oldAppName = getAppNameFromPath(oldPath);
  const newAppName = getAppNameFromPath(currentPath);

  if (oldAppName && !newAppName && NAVIGATION_CONFIG.forceRefreshOnReturnToMain) {
    console.log(`🚀 早期检测到从微前端 ${oldAppName} 返回主应用，立即显示页面刷新遮罩`);

    // 立即显示专门的页面刷新遮罩，这个遮罩会持续到页面刷新完成
    pageRefreshOverlay.show();

    // 保存目标路径
    sessionStorage.setItem('microapp_target_path', currentPath);

    // 延迟执行刷新，确保遮罩已显示
    setTimeout(() => {
      console.log('🔄 执行页面刷新');
      window.location.reload();
    }, 500); // 给遮罩更多时间显示和动画

    return; // 阻止后续的路径处理
  }

  // console.log(`⬅️ 浏览器后退/前进: ${currentPath}`, event.state);
  handlePathChange(currentPath, 'POP');
};

/**
 * 监听 pushState 和 replaceState
 */
const wrapHistoryMethods = () => {
  const originalPushState = window.history.pushState;
  const originalReplaceState = window.history.replaceState;

  window.history.pushState = function (state, title, url) {
    const result = originalPushState.apply(this, arguments);
    if (url) {
      const newPath = typeof url === 'string' ? url : url.pathname;
      handlePathChange(newPath, 'PUSH');
    }
    return result;
  };

  window.history.replaceState = function (state, title, url) {
    const result = originalReplaceState.apply(this, arguments);
    if (url) {
      const newPath = typeof url === 'string' ? url : url.pathname;
      handlePathChange(newPath, 'REPLACE');
    }
    return result;
  };

  // 保存原始方法的引用，用于恢复
  window.history._originalPushState = originalPushState;
  window.history._originalReplaceState = originalReplaceState;
};

/**
 * 恢复原始的 history 方法
 */
const unwrapHistoryMethods = () => {
  if (window.history._originalPushState) {
    window.history.pushState = window.history._originalPushState;
    delete window.history._originalPushState;
  }

  if (window.history._originalReplaceState) {
    window.history.replaceState = window.history._originalReplaceState;
    delete window.history._originalReplaceState;
  }
};

/**
 * 启动微前端导航处理器
 */
export const startMicroAppNavigationHandler = () => {
  if (isNavigationHandlerActive) {
    // console.log('🔄 微前端导航处理器已经启动');
    return;
  }

  // console.log('🚀 启动微前端导航处理器');

  // 预加载主应用资源
  preloadMainAppResources();

  // 检查是否有需要恢复的目标路径（页面刷新后）
  const targetPath = sessionStorage.getItem('microapp_target_path');
  if (targetPath && targetPath !== window.location.pathname) {
    // console.log(`🔄 页面刷新后恢复目标路径: ${targetPath}`);
    sessionStorage.removeItem('microapp_target_path');

    // 延迟导航，确保应用完全加载
    setTimeout(() => {
      window.history.replaceState(null, '', targetPath);
      lastKnownPath = targetPath;
      recordNavigation(targetPath, 'RESTORE');
    }, NAVIGATION_CONFIG.restoreDelay);
  } else {
    // 记录当前路径
    lastKnownPath = window.location.pathname;
    recordNavigation(lastKnownPath, 'INIT');
  }

  // 监听 popstate 事件（浏览器前进后退）
  window.addEventListener('popstate', handlePopState);

  // 监听语言切换事件
  window.addEventListener('languageChange', () => {
    recordLanguageChange();
  });

  // 包装 history 方法以监听程序化导航
  wrapHistoryMethods();

  isNavigationHandlerActive = true;

  // 检查当前路径是否需要重定向
  const currentPath = window.location.pathname;
  const appName = getAppNameFromPath(currentPath);
  if (appName && isAppRootPath(currentPath)) {
    const defaultRoute = getAppDefaultRoute(appName);
    // console.log(`🏠 启动时检测到根路径，重定向到: ${defaultRoute}`);
    window.history.replaceState(null, '', defaultRoute);
    lastKnownPath = defaultRoute;
    recordNavigation(defaultRoute, 'REPLACE');
  }
};

/**
 * 停止微前端导航处理器
 */
export const stopMicroAppNavigationHandler = () => {
  if (!isNavigationHandlerActive) {
    return;
  }

  // console.log('🛑 停止微前端导航处理器');

  // 移除事件监听器
  window.removeEventListener('popstate', handlePopState);

  // 恢复原始的 history 方法
  unwrapHistoryMethods();

  isNavigationHandlerActive = false;
};

/**
 * 检查导航处理器是否活跃
 * @returns {boolean} 是否活跃
 */
export const isNavigationHandlerRunning = () => {
  return isNavigationHandlerActive;
};

/**
 * 手动触发路径检查
 * @param {string} path - 要检查的路径（可选，默认使用当前路径）
 */
export const checkCurrentPath = (path = window.location.pathname) => {
  handlePathChange(path, 'CHECK');
};

/**
 * 更新导航配置
 * @param {Object} config - 配置对象
 */
export const updateNavigationConfig = (config) => {
  Object.assign(NAVIGATION_CONFIG, config);
  // console.log('🔧 导航配置已更新:', NAVIGATION_CONFIG);
};

/**
 * 获取当前导航配置
 * @returns {Object} 当前配置
 */
export const getNavigationConfig = () => {
  return { ...NAVIGATION_CONFIG };
};

/**
 * 启用/禁用强制刷新功能
 * @param {boolean} enabled - 是否启用
 */
export const setForceRefreshEnabled = (enabled) => {
  NAVIGATION_CONFIG.forceRefreshOnReturnToMain = enabled;
  // console.log(`🔧 强制刷新功能已${enabled ? '启用' : '禁用'}`);
};

/**
 * 预加载主应用关键资源
 */
const preloadMainAppResources = () => {
  if (!NAVIGATION_CONFIG.enablePreload) {
    return;
  }

  // 预加载关键CSS
  const criticalCSS = [
    '/src/styles/global.less',
    '/src/styles/simplebar.css'
  ];

  criticalCSS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    document.head.appendChild(link);
  });

  // 预加载关键JS模块
  const criticalJS = [
    '/src/components/MainCard.jsx',
    '/src/layout/MainLayout/index.jsx'
  ];

  criticalJS.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'modulepreload';
    link.href = href;
    document.head.appendChild(link);
  });
};

/**
 * 启用智能刷新模式
 * @param {boolean} enabled - 是否启用
 */
export const setSmartRefreshEnabled = (enabled) => {
  NAVIGATION_CONFIG.useSmartRefresh = enabled;
  // console.log(`🔧 智能刷新功能已${enabled ? '启用' : '禁用'}`);
};

// 导出默认对象
export default {
  startMicroAppNavigationHandler,
  stopMicroAppNavigationHandler,
  isNavigationHandlerRunning,
  checkCurrentPath,
  getNavigationHistory,
  clearNavigationHistory,
  updateNavigationConfig,
  getNavigationConfig,
  setForceRefreshEnabled,
  setSmartRefreshEnabled
};
