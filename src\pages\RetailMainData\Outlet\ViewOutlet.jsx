import React from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { pxToRem } from "@u/zkUtils";
import { getOutletDetail, getCustomOutletTypeList } from "@s/api/outlet";
import { useLocation, useNavigate } from "react-router-dom";
import { useClientId, useClientCode } from "@/hooks/client.js";
import ViewBox from "@/components/ViewBox";
function ViewOutlet(props) {
  const navigate = useNavigate();
  // 国际化
  const { t } = useTranslation();
  const { state } = useLocation();
  const [data, setData] = useState([]);
  const [customTag, setCustomTag] = useState([]);
  const [newLabels, setNewLabels] = useState([]);
  const clientId = useClientId();
  const clinetCode = useClientCode();
  if (clientId) {
    sessionStorage.setItem("CLIENT_ID", clientId);
    sessionStorage.setItem("CLIENT_CODE", clinetCode);
  }
  useEffect(() => {
    getCustomOutletTypeList(sessionStorage.getItem("CLIENT_ID")).then((res) => {
      if (res?.data) {
        setNewLabels(res?.data);
      }
    });

    getOutletDetail(state?.id).then((res) => {
      if (res?.code == "00000000") {
        let data = res?.data;
        setCustomTag(data?.outletTypeValueList);

        setData(data);
      }
    });
  }, []);

  let TextStyle = {
    font: `normal normal normal 14px/17px Proxima Nova`,
    color: "#474B4F",
    opacity: "0.5",
  };

  let contentStyle = {
    font: `normal normal medium 16px/19px Proxima Nova`,
    color: "#474B4F",
  };

  return (
    <div
      style={{
        height: "100%",
      }}>
      <RightViewLayout
        title={t("outlets.view_outlet")}
        navigateBack={"/retail/list"}
        handleCancle={() => {
          navigate("/retail/list");
        }}
        isShowSave={false}>
        <Grid
          container
          direction={"column"}
          justifyContent={"space-evenly"}
          sx={{
            fontSize: pxToRem(16),
          }}>
          {/* <Grid mt={4}>
            <Avatar
              className="avatar radial-button"
              alt="ZK"
              src={data?.photo}
              sx={{
                width: "110px",
                height: "110px",
                borderRadius: "8px",
              }}></Avatar>
          </Grid> */}

          <Grid item mt={3}>
            <Typography sx={TextStyle}>
              {t("outlets.organization_name")}
            </Typography>
            <Typography sx={contentStyle}>{data?.name}</Typography>
            <Typography sx={contentStyle}>{data?.user?.lastName}</Typography>
          </Grid>

          <Grid item mt={3}>
            <Typography sx={TextStyle}>{t("outlets.region")}</Typography>
            <Typography sx={contentStyle}>{data?.code}</Typography>
          </Grid>

          <Grid item mt={3}>
            <Typography sx={TextStyle}>
              {t("outlets.organization_owner_email")}
            </Typography>
            <Typography sx={contentStyle}>{data?.email}</Typography>
          </Grid>

          <Grid item mt={3}>
            <Typography sx={TextStyle}>
              {t("outlets.organization_owner_mobile")}
            </Typography>

            <Typography sx={contentStyle}>
              {`+ ${data?.countryCode} ${data?.phone}`}
            </Typography>
          </Grid>

          {newLabels?.map((item, index) => {
            let response = customTag?.find((list) => {
              return list?.outletTypeId == item?.id;
            });

            return (
              <Grid item mt={3} key={item?.id + index}>
                <ViewBox title={item?.name} content={response?.value} />
              </Grid>
            );
          })}
        </Grid>
      </RightViewLayout>
    </div>
  );
}

export default ViewOutlet;
