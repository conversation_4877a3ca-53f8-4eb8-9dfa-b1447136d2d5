# 密码修改组件国际化文档

## 概述

本文档记录了 `PasswordChangeDialog` 组件的国际化实现，包括所有需要翻译的文字内容和对应的翻译键。

## 已国际化的内容

### 1. 组件标题和标签
| 中文 | 英文 | 翻译键 |
|------|------|--------|
| 修改密码 | Change Password | `common.password_change_title` |
| 当前密码 | Current Password | `common.password_change_current_label` |
| 新密码 | New Password | `common.password_change_new_label` |
| 确认新密码 | Confirm New Password | `common.password_change_confirm_label` |

### 2. 步骤标签
| 中文 | 英文 | 翻译键 |
|------|------|--------|
| 验证当前密码 | Verify Current Password | `common.password_change_step_verify` |
| 设置新密码 | Set New Password | `common.password_change_step_set_new` |

### 3. 按钮文字
| 中文 | 英文 | 翻译键 |
|------|------|--------|
| 取消 | Cancel | `common.password_change_btn_cancel` |
| 验证密码 | Verify Password | `common.password_change_btn_verify` |
| 确认修改 | Confirm Change | `common.password_change_btn_confirm` |
| 处理中... | Processing... | `common.password_change_btn_processing` |

### 4. 错误和提示信息
| 中文 | 英文 | 翻译键 |
|------|------|--------|
| 请输入当前密码 | Please enter current password | `common.password_change_error_current_required` |
| 当前密码不正确，请重新输入 | Current password is incorrect, please try again | `common.password_change_error_current_incorrect` |
| 验证失败，请重试 | Verification failed, please try again | `common.password_change_error_verify_failed` |
| 密码验证成功 | Password verification successful | `common.password_change_success_verified` |
| 请确认新密码 | Please confirm new password | `common.password_change_error_confirm_required` |
| 两次输入的密码不一致 | Passwords do not match | `common.password_change_error_passwords_not_match` |
| 密码强度不够，请设置更强的密码 | Password strength is insufficient, please set a stronger password | `common.password_change_error_password_weak` |
| 密码修改成功 | Password changed successfully | `common.password_change_success_changed` |
| 密码修改失败，请重试 | Password change failed, please try again | `common.password_change_error_change_failed` |

## 使用方法

### 1. 基础使用
```jsx
import PasswordChangeDialog from '@/components/PasswordChange/PasswordChangeDialog';

<PasswordChangeDialog
  open={open}
  onClose={handleClose}
  onPasswordChange={handlePasswordChange}
/>
```

### 2. 自定义标签（可选）
```jsx
<PasswordChangeDialog
  open={open}
  onClose={handleClose}
  onPasswordChange={handlePasswordChange}
  title="自定义标题"
  oldPasswordLabel="自定义当前密码标签"
  newPasswordLabel="自定义新密码标签"
  confirmPasswordLabel="自定义确认密码标签"
/>
```

## 翻译文件位置

### 中文翻译
文件：`src/lang/modules/zh/base.js`
位置：在 `common` 对象的末尾添加了密码修改相关的翻译

### 英文翻译
文件：`src/lang/modules/en/base.js`
位置：在 `common` 对象的末尾添加了密码修改相关的翻译

## 实现细节

### 1. 导入国际化Hook
```jsx
import { useTranslation } from 'react-i18next';

const PasswordChangeDialog = ({ ... }) => {
  const { t } = useTranslation();
  // ...
};
```

### 2. 使用翻译键
```jsx
// 使用国际化的默认值
const dialogTitle = title || t("common.password_change_title");
const currentPasswordLabel = oldPasswordLabel || t("common.password_change_current_label");
```

### 3. 动态文字替换
```jsx
// 错误信息
setError(t("common.password_change_error_current_incorrect"));

// 成功信息
setSuccess(t("common.password_change_success_verified"));

// 按钮文字
{loading 
  ? t("common.password_change_btn_processing") 
  : step === 0 
    ? t("common.password_change_btn_verify") 
    : t("common.password_change_btn_confirm")
}
```

## 注意事项

1. **向后兼容性**：组件仍然支持通过 props 传入自定义标签，如果没有传入则使用国际化的默认值。

2. **翻译键命名规范**：所有翻译键都以 `common.password_change_` 开头，便于管理和查找。

3. **错误处理**：如果翻译键不存在，会显示翻译键本身，便于调试。

4. **扩展性**：新增的翻译键都放在 `common` 对象中，便于在其他组件中复用。

## 测试建议

1. **语言切换测试**：测试在中英文之间切换时，所有文字是否正确显示。

2. **缺失翻译测试**：删除某个翻译键，确认是否有合适的降级处理。

3. **自定义标签测试**：传入自定义标签，确认是否覆盖默认的国际化文字。

4. **长文本测试**：测试较长的翻译文字是否会影响界面布局。
