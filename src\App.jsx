import React, { useEffect, useState, useCallback } from "react";
import { registerMicroApps, start } from "qiankun";
import { ToastContainer } from "react-toastify";

// 样式和国际化
import "@/lang/index";
import "@/styles/global.less";

// 组件导入
import RouterWaiter from "./components/routerWaiter";
import ThemeCustomization from "./themes";
import { ConfirmProvider } from "./components/zkconfirm";
import ScrollTop from "@c/ScrollTop";
// import ErrorBoundary from "./router/ExceptionComponent/ErrorBoundary";
import LoadingOverlay from "./components/LoadingOverlay";
import globalLoadingManager from "./utils/globalLoadingManager";
import pageRefreshOverlay from "./utils/pageRefreshOverlay";
import { initMicroAppI18nSync } from "./utils/microAppI18nSync";
import { initMicroAppContainerManager } from "./utils/microAppContainerManager";

// 路由和工具
import routes from "./router/routers";
import onRouteBefore from "./router/onRouteBefore";
import { initGlobalErrorHandler } from "./utils/globalErrorHandler";
import { startMicroAppNavigationHandler } from "./utils/microAppNavigationHandler";

// 微前端配置导入
import {
  createMicroAppConfig,
  QIANKUN_CONFIG,
  TOAST_CONFIG,
  checkEnvironmentConfig,
} from "./config/microAppConfig";

const App = () => {
  const [qiankunStarted, setQiankunStarted] = useState(false);
  const [loadingState, setLoadingState] = useState({
    isLoading: false,
    message: "正在加载...",
    progress: 0,
    showProgress: false,
  });

  // 注册微前端应用
  const registerMicroAppsWithConfig = useCallback(() => {
    try {
      if (!checkEnvironmentConfig()) {
        return false;
      }

      const microAppsConfig = createMicroAppConfig();
      registerMicroApps(microAppsConfig);

      // 启动qiankun
      start(QIANKUN_CONFIG);
      return true;
    } catch (error) {
      // console.error("微前端应用注册失败:", error);
      // 即使注册失败也不跳转到500页面，让主应用继续运行
      return false;
    }
  }, []);

  // 全局启动qiankun的函数
  const startQiankun = useCallback(() => {
    if (!qiankunStarted) {
      // 确保在启动 qiankun 前有基础容器（如果需要的话）
      console.log("🚀 准备启动 qiankun");

      const success = registerMicroAppsWithConfig();
      if (success) {
        setQiankunStarted(true);
        console.log("✅ Qiankun started successfully");
      } else {
        console.error("❌ Qiankun 启动失败");
      }
    }
  }, [qiankunStarted, registerMicroAppsWithConfig]);

  // 将启动函数挂载到全局，供MicroAppContainer使用，并在应用启动时初始化
  useEffect(() => {
    window.__QIANKUN_START__ = startQiankun;

    // 在应用启动时就初始化容器管理器和 qiankun
    const initMicroApps = async () => {
      try {
        // 1. 首先初始化容器管理器
        console.log("🔧 初始化微前端容器管理器");
        initMicroAppContainerManager();

        // 2. 延迟一点时间确保主应用完全加载后再启动 qiankun
        setTimeout(() => {
          console.log("🚀 应用启动时初始化 qiankun");
          startQiankun();
        }, 1000);
      } catch (error) {
        console.error("❌ 微前端初始化失败:", error);
      }
    };

    initMicroApps();

    return () => {
      delete window.__QIANKUN_START__;
    };
  }, [startQiankun]);

  useEffect(() => {
    // 初始化全局错误处理器
    initGlobalErrorHandler();

    // 启动微前端导航处理器
    startMicroAppNavigationHandler();

    // 初始化微前端国际化同步
    initMicroAppI18nSync();

    // 监听全局加载状态
    const unsubscribe = globalLoadingManager.addListener(setLoadingState);

    // 检查是否有页面刷新遮罩需要隐藏
    const checkAndHideRefreshOverlay = () => {
      if (pageRefreshOverlay.isVisible()) {
        console.log("🎭 检测到页面刷新遮罩，准备隐藏");
        // 延迟隐藏，确保主应用完全加载和渲染
        setTimeout(() => {
          pageRefreshOverlay.hide();
          console.log("🎭 页面刷新遮罩已隐藏");
        }, 1200); // 增加延迟时间，确保页面完全渲染
      }
    };

    // 立即检查
    checkAndHideRefreshOverlay();

    // 也在页面完全加载后再次检查
    if (document.readyState === "complete") {
      checkAndHideRefreshOverlay();
    } else {
      window.addEventListener("load", checkAndHideRefreshOverlay);
    }

    return () => {
      unsubscribe();
      window.removeEventListener("load", checkAndHideRefreshOverlay);
    };
  }, []);

  return (
    // <ErrorBoundary>
    <ThemeCustomization>
      <ConfirmProvider>
        <ScrollTop>
          <RouterWaiter routes={routes} onRouteBefore={onRouteBefore} />
          <ToastContainer {...TOAST_CONFIG} />
          <LoadingOverlay
            visible={loadingState.isLoading}
            message={loadingState.message}
            progress={loadingState.progress}
            showProgress={loadingState.showProgress}
            type={loadingState.type}
          />
        </ScrollTop>
      </ConfirmProvider>
    </ThemeCustomization>
    // </ErrorBoundary>
  );
};

export default App;
