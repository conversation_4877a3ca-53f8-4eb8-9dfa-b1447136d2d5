# Material-UI 风格的 TreeSelect 组件

这个组件完全兼容 Ant Design TreeSelect API，但使用了 Material-UI 的设计风格和组件。

## 🎨 Material-UI 设计特色

### 视觉设计
- **圆角设计**: 使用 Material-UI 的 `borderRadius` 系统
- **阴影效果**: 采用 Material-UI 的 `shadows` 系统
- **颜色系统**: 完全集成 Material-UI 主题色彩
- **动画过渡**: 使用 Material-UI 的 `transitions` 系统

### 组件结构
- **输入框**: 使用 `TextField` 而非原生 input
- **下拉框**: 使用 `Popover` 提供更好的定位
- **树结构**: 使用 `List` + `ListItemButton` 替代 TreeView
- **搜索框**: 集成 Material-UI 的搜索样式
- **图标**: 使用 Material-UI 图标库

## 🚀 快速开始

### 基本用法

```jsx
import TreeSelect from '@/components/AntdTreeSelect';

const treeData = [
  {
    title: '技术部',
    value: 'tech',
    children: [
      { title: '前端组', value: 'frontend' },
      { title: '后端组', value: 'backend' },
    ],
  },
];

function MyComponent() {
  const [value, setValue] = useState();
  
  return (
    <TreeSelect
      value={value}
      onChange={setValue}
      treeData={treeData}
      placeholder="请选择部门"
      showSearch
      allowClear
      size="medium"
      variant="outlined"
    />
  );
}
```

### Material-UI 主题集成

```jsx
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  shape: {
    borderRadius: 12, // 影响组件圆角
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <TreeSelect
        // ... props
        // 自动使用主题样式
      />
    </ThemeProvider>
  );
}
```

## 🎛️ 样式定制

### 自定义样式

```jsx
<TreeSelect
  sx={{
    '& .MuiTextField-root': {
      '& .MuiOutlinedInput-root': {
        borderRadius: 3,
        backgroundColor: 'background.paper',
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      },
    },
  }}
  // ... 其他 props
/>
```

### 尺寸变体

```jsx
{/* 小尺寸 */}
<TreeSelect size="small" />

{/* 中等尺寸（默认） */}
<TreeSelect size="medium" />

{/* 大尺寸 */}
<TreeSelect size="large" />
```

### 输入框变体

```jsx
{/* 轮廓样式（默认） */}
<TreeSelect variant="outlined" />

{/* 填充样式 */}
<TreeSelect variant="filled" />

{/* 标准样式 */}
<TreeSelect variant="standard" />
```

## 🎯 高级功能

### 多选模式

```jsx
<TreeSelect
  multiple
  value={multipleValue}
  onChange={setMultipleValue}
  treeData={treeData}
  maxTagCount={3}
  maxTagPlaceholder={(omitted) => `+${omitted.length} 更多`}
/>
```

### 可勾选模式

```jsx
<TreeSelect
  treeCheckable
  value={checkableValue}
  onChange={setCheckableValue}
  treeData={treeData}
  showCheckedStrategy={TreeSelect.SHOW_PARENT}
/>
```

### 搜索功能

```jsx
<TreeSelect
  showSearch
  filterTreeNode={(inputValue, treeNode) => {
    return treeNode.title.toLowerCase().includes(inputValue.toLowerCase());
  }}
  // ... 其他 props
/>
```

### 异步加载

```jsx
const loadData = ({ value }) => {
  return new Promise(resolve => {
    setTimeout(() => {
      // 更新 treeData
      resolve();
    }, 1000);
  });
};

<TreeSelect
  treeData={asyncTreeData}
  loadData={loadData}
  // ... 其他 props
/>
```

## 🎨 Material-UI 特有功能

### 图标定制

```jsx
import { Folder, FolderOpen, InsertDriveFile } from '@mui/icons-material';

<TreeSelect
  treeIcon={true} // 显示默认图标
  // 或自定义图标
  treeIcon={<Folder />}
  // ... 其他 props
/>
```

### 状态指示

```jsx
<TreeSelect
  status="error"    // 错误状态
  status="warning"  // 警告状态
  // ... 其他 props
/>
```

### 前缀和后缀

```jsx
import { Search, ArrowDropDown } from '@mui/icons-material';

<TreeSelect
  prefix={<Search />}
  suffixIcon={<ArrowDropDown />}
  // ... 其他 props
/>
```

## 🔧 与原版差异

### 视觉差异
1. **圆角**: 更圆润的边角设计
2. **阴影**: 更柔和的阴影效果
3. **颜色**: 使用 Material-UI 主题色彩
4. **动画**: 更流畅的过渡动画

### 功能增强
1. **主题集成**: 完全集成 Material-UI 主题系统
2. **响应式**: 更好的移动端适配
3. **无障碍**: 更好的可访问性支持
4. **性能**: 优化的渲染性能

### API 兼容性
- ✅ 100% 兼容 Ant Design TreeSelect API
- ✅ 支持所有原有属性和方法
- ✅ 支持所有事件回调
- ✅ 支持所有数据格式

## 📱 响应式设计

组件自动适配不同屏幕尺寸：

```jsx
// 自动响应式
<TreeSelect
  // 在小屏幕上自动调整
  popupMatchSelectWidth={true}
  // ... 其他 props
/>

// 手动控制
import { useMediaQuery, useTheme } from '@mui/material';

function ResponsiveTreeSelect() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  return (
    <TreeSelect
      size={isMobile ? 'small' : 'medium'}
      maxTagCount={isMobile ? 1 : 3}
      // ... 其他 props
    />
  );
}
```

## 🎯 最佳实践

1. **主题一致性**: 确保与应用主题保持一致
2. **性能优化**: 大数据量时使用虚拟滚动
3. **用户体验**: 提供清晰的加载和错误状态
4. **无障碍**: 确保键盘导航和屏幕阅读器支持

## 🔗 相关链接

- [Material-UI 官方文档](https://mui.com/)
- [Ant Design TreeSelect API](https://ant.design/components/tree-select/)
- [Material Design 规范](https://material.io/design)
