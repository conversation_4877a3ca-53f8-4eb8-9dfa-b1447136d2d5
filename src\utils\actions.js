//actions.js 具体代码如下
import { initGlobalState } from 'qiankun'
import { getStoreLang } from '@/utils/langUtils'

// 初始化全局状态，包含语言信息
const initialState = {
    language: getStoreLang(), // 当前语言
    user: null, // 用户信息
    token: null // 令牌信息
}

const actions = initGlobalState(initialState)

// 导出更新语言的方法
export const updateLanguage = (language) => {
    actions.setGlobalState({
        language
    })
}

// 导出更新用户信息的方法
export const updateUser = (user) => {
    actions.setGlobalState({
        user
    })
}

// 导出更新令牌的方法
export const updateToken = (token) => {
    actions.setGlobalState({
        token
    })
}

export default actions
