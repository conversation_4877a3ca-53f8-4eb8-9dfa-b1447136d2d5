import RegexCode from "@/enums/RegexCode";
import ZKSearchTree from "../../../ZKSearchTree.jsx";
export const getFormConfig = (t, type, treeSelectRef) => {
  let formConfig = [
    {
      name: "name",
      label: t("partner.partner_name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.enter_partner_name"),
        },
      ],
    },
    {
      name: "email",
      label: t("partner.email"),
      type: "input",
      required: true,
      disabled: type == "editor" ? true : false,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("partner.email_required"),
        },
        {
          type: "email",
          message: t("partner.email_format_error"),
        },
      ],
    },

    {
      codename: "countryCode",
      name: "phone",
      label: t("partner.mobile_number"),
      type: "mobile",
      required: false,
      validation: [
        {
          type: "string",
          message: "",
        },
        // {
        //   type: "required",
        //   message: t("partner.mobile_number_required"),
        // },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("partner.mobile_format_error"),
        },
      ],
    },

    {
      name: "areaId",
      required: true,
      custom: true,
      label: t("common.common_area_name"),
      renderingCustomItem: (item, formik) => {
        return (
          <Grid xs={6} pl={3} mt={3}>
            <ZKSearchTree ref={treeSelectRef} formik={formik} {...item} />
          </Grid>
        );
      },

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_region_required"),
        },
      ],
    },

    // {
    //   name: "password",
    //   label: t("partner.password"),
    //   type: "password",
    //   viewPwd: true,
    //   required: true,
    //   display: type !== "editor" ? true : false,
    //   validation:
    //     type !== "editor"
    //       ? [
    //           {
    //             type: "string",
    //             message: "",
    //           },
    //           {
    //             type: "required",
    //             message: t("partner.password_required"),
    //           },
    //           {
    //             type: "matches",
    //             matches: RegexCode.PASSWORD_REGEX,
    //             message: t("partner.password_format_error"),
    //           },
    //         ]
    //       : null,
    // },

    // {
    //   name: "confirmPassword",
    //   label: t("partner.confirm_password"),
    //   type: "password",
    //   viewPwd: true,
    //   display: type !== "editor" ? true : false,
    //   required: true,
    //   validation:
    //     type !== "editor"
    //       ? [
    //           {
    //             type: "string",
    //             message: "",
    //           },
    //           {
    //             type: "required",
    //             message: t("partner.confirm_password_required"),
    //           },
    //           {
    //             type: "secondConfirm",
    //             ref: "password",
    //             message: t("partner.password_mismatch"),
    //           },
    //         ]
    //       : null,
    // },
  ];

  return formConfig;
};
